#!/usr/bin/env python3
"""
Test script pre simulate_live_trading.py
"""
import subprocess
import sys
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO)
log = logging.getLogger("TestSimulateLiveTrading")

def test_simulate_live_trading():
    """Test simulate_live_trading.py"""
    log.info("=== TESTING SIMULATE LIVE TRADING ===")
    
    # Check if required files exist
    required_files = [
        "simulate_live_trading.py",
        "strategyConfig_scalp_1s.json"
    ]
    
    for file in required_files:
        if not Path(file).exists():
            log.error(f"❌ Required file not found: {file}")
            return False
    
    # Check if model files exist
    model_files = list(Path(".").glob("sac_*_steps.zip"))
    vecnorm_files = list(Path(".").glob("vecnorm_*.pkl")) + list(Path(".").glob("sac_*.vecnorm.pkl"))
    
    if not model_files:
        log.error("❌ No SAC model files found!")
        return False
    
    if not vecnorm_files:
        log.error("❌ No VecNormalize files found!")
        return False
    
    log.info(f"✅ Found {len(model_files)} model files")
    log.info(f"✅ Found {len(vecnorm_files)} vecnorm files")
    
    # Check if parquet data exists
    parquet_dir = Path("parquet_processed/XRPUSDC/1s")
    if not parquet_dir.exists():
        log.error(f"❌ Parquet data directory not found: {parquet_dir}")
        log.info("Please run data processing first to create parquet files")
        return False
    
    # Find available dates
    parquet_files = list(parquet_dir.glob("*.parquet"))
    if not parquet_files:
        log.error(f"❌ No parquet files found in {parquet_dir}")
        return False
    
    log.info(f"✅ Found {len(parquet_files)} parquet files")
    
    # Get a recent date for testing
    dates = [f.stem for f in parquet_files]
    dates.sort()
    
    if len(dates) < 2:
        log.error("❌ Need at least 2 days of data for testing")
        return False
    
    # Use last 2 days for testing
    start_date = dates[-2]
    end_date = dates[-1]
    
    log.info(f"📊 Testing with dates: {start_date} to {end_date}")
    
    # Test command
    cmd = [
        "python3", "simulate_live_trading.py",
        "--cfg", "strategyConfig_scalp_1s.json",
        "--start", start_date,
        "--end", end_date,
        "--out-trades", "test_sim_trades.csv",
        "--out-equity", "test_sim_equity.csv",
        "--log-level", "INFO"
    ]
    
    log.info(f"🚀 Running command: {' '.join(cmd)}")
    
    try:
        # Run the simulation
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            log.info("✅ Simulation completed successfully!")
            log.info("STDOUT:")
            for line in result.stdout.split('\n')[-20:]:  # Last 20 lines
                if line.strip():
                    log.info(f"  {line}")
            
            # Check output files
            if Path("test_sim_trades.csv").exists():
                log.info("✅ Trades file created")
            else:
                log.warning("⚠️ Trades file not created")
            
            if Path("test_sim_equity.csv").exists():
                log.info("✅ Equity file created")
            else:
                log.warning("⚠️ Equity file not created")
            
            return True
        else:
            log.error(f"❌ Simulation failed with return code: {result.returncode}")
            log.error("STDERR:")
            for line in result.stderr.split('\n'):
                if line.strip():
                    log.error(f"  {line}")
            
            log.error("STDOUT:")
            for line in result.stdout.split('\n'):
                if line.strip():
                    log.error(f"  {line}")
            
            return False
            
    except subprocess.TimeoutExpired:
        log.error("❌ Simulation timed out after 5 minutes")
        return False
    except Exception as e:
        log.error(f"❌ Error running simulation: {e}")
        return False

def test_import():
    """Test importing simulate_live_trading module"""
    log.info("\n=== TESTING IMPORT ===")
    
    try:
        import simulate_live_trading
        log.info("✅ Module imported successfully")
        
        # Check if main functions exist
        required_functions = [
            'load_config',
            'load_historical_data', 
            'simulate_live_trading_logic',
            'simulate_trading_loop',
            'main'
        ]
        
        for func_name in required_functions:
            if hasattr(simulate_live_trading, func_name):
                log.info(f"✅ Function {func_name} found")
            else:
                log.error(f"❌ Function {func_name} not found")
                return False
        
        return True
        
    except Exception as e:
        log.error(f"❌ Import failed: {e}")
        return False

def main():
    """Main test function"""
    log.info("🧪 TESTING SIMULATE LIVE TRADING")
    log.info("=" * 60)
    
    # Test import
    import_ok = test_import()
    
    # Test simulation (only if import works)
    simulation_ok = False
    if import_ok:
        simulation_ok = test_simulate_live_trading()
    
    log.info("=" * 60)
    log.info("🏁 TEST RESULTS")
    log.info("=" * 60)
    
    log.info(f"Import test: {'✅ PASS' if import_ok else '❌ FAIL'}")
    log.info(f"Simulation test: {'✅ PASS' if simulation_ok else '❌ FAIL'}")
    
    if import_ok and simulation_ok:
        log.info("\n🎉 ALL TESTS PASSED!")
        log.info("💡 simulate_live_trading.py is ready to use!")
        log.info("🚀 You can now test live trading logic with historical data!")
        
        log.info("\n📖 Usage example:")
        log.info("python3 simulate_live_trading.py \\")
        log.info("  --cfg strategyConfig_scalp_1s.json \\")
        log.info("  --start 2025-07-05 \\")
        log.info("  --end 2025-07-07 \\")
        log.info("  --out-trades test_trades.csv \\")
        log.info("  --out-equity test_equity.csv")
        
    else:
        log.error("\n❌ Some tests failed!")
        log.error("Fix the issues before using simulate_live_trading.py")
    
    return import_ok and simulation_ok

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
