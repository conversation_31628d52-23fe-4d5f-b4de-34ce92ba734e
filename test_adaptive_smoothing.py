#!/usr/bin/env python3
"""
Test script for adaptive signal smoothing and threshold tuning

This script tests the new adaptive signal processing modules with synthetic data
to verify they work correctly before integrating into the main trading system.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import json
from signal_smoothing import SignalSmoother
from adaptive_thresholds import AdaptiveThresholdTuner, create_threshold_tuner_from_config
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)

def generate_synthetic_signals(n_samples=1000, noise_level=0.1, trend_strength=0.02):
    """Generate synthetic trading signals for testing"""
    
    # Create base trend
    t = np.linspace(0, 10, n_samples)
    trend = np.sin(t * 0.5) * trend_strength
    
    # Add noise
    noise = np.random.normal(0, noise_level, n_samples)
    
    # Create spiky neural network-like signals
    spikes = np.random.choice([-1, 0, 1], n_samples, p=[0.05, 0.9, 0.05]) * 0.3
    
    # Combine components
    raw_signals = trend + noise + spikes
    
    # Create ATR-like volatility data
    atr_base = 0.02
    atr_volatility = atr_base * (1 + 0.5 * np.sin(t * 0.3) + 0.2 * np.random.randn(n_samples))
    atr_ratios = atr_volatility / atr_base
    
    return raw_signals, atr_ratios

def test_smoothing_methods():
    """Test different smoothing methods"""
    
    log.info("🧪 Testing smoothing methods...")
    
    # Generate test data
    raw_signals, atr_ratios = generate_synthetic_signals(500, noise_level=0.15)
    
    # Test different smoothing methods
    methods = ['ema', 'kama', 'median_ema', 'kalman', 'trend_confirm']
    
    results = {}
    
    for method in methods:
        log.info(f"Testing {method} method...")
        
        # Configure method-specific parameters
        if method == 'ema':
            smoother = SignalSmoother(method=method, emaAlpha=0.7)
        elif method == 'kama':
            smoother = SignalSmoother(method=method, kamaFastSC=0.666, kamaSlowSC=0.064, kamaLookback=10)
        elif method == 'median_ema':
            smoother = SignalSmoother(method=method, medianWindow=5, emaAlpha=0.4)
        elif method == 'kalman':
            smoother = SignalSmoother(method=method, kalmanProcessNoise=0.01, kalmanMeasurementNoise=0.1)
        elif method == 'trend_confirm':
            smoother = SignalSmoother(method=method, trendEmaShort=3, trendEmaLong=8, emaAlpha=0.7)
        
        smoothed_signals = []
        confidences = []
        
        for i, (raw_sig, atr_ratio) in enumerate(zip(raw_signals, atr_ratios)):
            result = smoother.update(raw_sig, atr_ratio)
            smoothed_signals.append(result['smoothed_signal'])
            confidences.append(result['confidence'])
        
        results[method] = {
            'smoothed': np.array(smoothed_signals),
            'confidence': np.array(confidences),
            'smoother': smoother
        }
        
        # Calculate performance metrics
        smoothing_effect = np.mean(np.abs(np.array(smoothed_signals) - raw_signals))
        stability = 1.0 / (1.0 + np.std(np.diff(smoothed_signals)))
        avg_confidence = np.mean(confidences)
        
        log.info(f"  {method}: smoothing_effect={smoothing_effect:.4f}, stability={stability:.4f}, avg_confidence={avg_confidence:.4f}")
    
    # Plot results
    fig, axes = plt.subplots(len(methods) + 1, 1, figsize=(12, 2 * (len(methods) + 1)))
    
    # Plot raw signals
    axes[0].plot(raw_signals, label='Raw signals', alpha=0.7, color='gray')
    axes[0].set_title('Raw Signals')
    axes[0].legend()
    axes[0].grid(True)
    
    # Plot smoothed signals
    colors = ['blue', 'red', 'green', 'orange', 'purple']
    for i, (method, color) in enumerate(zip(methods, colors)):
        ax = axes[i + 1]
        ax.plot(raw_signals, label='Raw', alpha=0.3, color='gray')
        ax.plot(results[method]['smoothed'], label=f'{method} smoothed', color=color)
        ax.plot(results[method]['confidence'], label='Confidence', alpha=0.5, linestyle='--', color=color)
        ax.set_title(f'{method.upper()} Method')
        ax.legend()
        ax.grid(True)
    
    plt.tight_layout()
    plt.savefig('smoothing_methods_comparison.png', dpi=150, bbox_inches='tight')
    log.info("📊 Smoothing comparison plot saved as 'smoothing_methods_comparison.png'")
    
    return results

def test_adaptive_thresholds():
    """Test adaptive threshold tuning"""
    
    log.info("🎯 Testing adaptive threshold tuning...")
    
    # Create test configuration
    test_config = {
        'tradeParams': {
            'longEntryThreshold': 0.6,
            'shortEntryThreshold': -0.6,
            'exitActionThreshold': 0.5,
            'thresholdTuning': {
                'confidenceWeight': 0.5,
                'volatilityWeight': 0.2,
                'adaptiveThresholdsEnabled': True
            }
        }
    }
    
    # Create threshold tuner
    tuner = create_threshold_tuner_from_config(test_config)
    
    # Generate test scenarios
    scenarios = [
        {'confidence': 0.9, 'atr_ratio': 1.0, 'name': 'High confidence, normal volatility'},
        {'confidence': 0.3, 'atr_ratio': 1.0, 'name': 'Low confidence, normal volatility'},
        {'confidence': 0.7, 'atr_ratio': 2.0, 'name': 'Medium confidence, high volatility'},
        {'confidence': 0.7, 'atr_ratio': 0.5, 'name': 'Medium confidence, low volatility'},
        {'confidence': 0.1, 'atr_ratio': 3.0, 'name': 'Very low confidence, very high volatility'},
    ]
    
    log.info("Testing different market scenarios:")
    for scenario in scenarios:
        long_thr, short_thr, exit_thr = tuner.calculate_adaptive_thresholds(
            signal_confidence=scenario['confidence'],
            atr_ratio=scenario['atr_ratio']
        )
        
        log.info(f"  {scenario['name']}:")
        log.info(f"    Confidence: {scenario['confidence']:.2f}, ATR ratio: {scenario['atr_ratio']:.2f}")
        log.info(f"    Adaptive thresholds: Long={long_thr:.3f}, Short={short_thr:.3f}, Exit={exit_thr:.3f}")
        log.info(f"    Base thresholds: Long={tuner.base_long_threshold:.3f}, Short={tuner.base_short_threshold:.3f}, Exit={tuner.base_exit_threshold:.3f}")
    
    # Test threshold statistics
    stats = tuner.get_threshold_statistics()
    log.info(f"📈 Threshold adjustment statistics: {stats}")
    
    return tuner

def test_integrated_system():
    """Test integrated smoothing + adaptive thresholds"""
    
    log.info("🔄 Testing integrated adaptive system...")
    
    # Generate longer test sequence
    raw_signals, atr_ratios = generate_synthetic_signals(1000, noise_level=0.2)
    
    # Setup integrated system
    smoother = SignalSmoother(method='kama', kamaFastSC=0.666, kamaSlowSC=0.064)
    
    test_config = {
        'tradeParams': {
            'longEntryThreshold': 0.5,
            'shortEntryThreshold': -0.5,
            'exitActionThreshold': 0.4,
            'thresholdTuning': {
                'confidenceWeight': 0.4,
                'volatilityWeight': 0.3,
                'adaptiveThresholdsEnabled': True
            }
        }
    }
    tuner = create_threshold_tuner_from_config(test_config)
    
    # Process signals
    results = []
    for i, (raw_sig, atr_ratio) in enumerate(zip(raw_signals, atr_ratios)):
        # Smooth signal
        smooth_result = smoother.update(raw_sig, atr_ratio)
        smoothed_sig = smooth_result['smoothed_signal']
        confidence = smooth_result['confidence']
        
        # Calculate adaptive thresholds
        long_thr, short_thr, exit_thr = tuner.calculate_adaptive_thresholds(
            signal_confidence=confidence,
            atr_ratio=atr_ratio
        )
        
        # Check for entries
        long_entry = smoothed_sig >= long_thr
        short_entry = smoothed_sig <= short_thr
        
        results.append({
            'step': i,
            'raw_signal': raw_sig,
            'smoothed_signal': smoothed_sig,
            'confidence': confidence,
            'atr_ratio': atr_ratio,
            'long_threshold': long_thr,
            'short_threshold': short_thr,
            'long_entry': long_entry,
            'short_entry': short_entry
        })
    
    # Convert to DataFrame for analysis
    df = pd.DataFrame(results)
    
    # Calculate statistics
    total_long_entries = df['long_entry'].sum()
    total_short_entries = df['short_entry'].sum()
    avg_confidence = df['confidence'].mean()
    threshold_range_long = (df['long_threshold'].min(), df['long_threshold'].max())
    threshold_range_short = (df['short_threshold'].min(), df['short_threshold'].max())
    
    log.info("📊 Integrated system results:")
    log.info(f"  Total long entries: {total_long_entries}")
    log.info(f"  Total short entries: {total_short_entries}")
    log.info(f"  Average confidence: {avg_confidence:.3f}")
    log.info(f"  Long threshold range: [{threshold_range_long[0]:.3f}, {threshold_range_long[1]:.3f}]")
    log.info(f"  Short threshold range: [{threshold_range_short[0]:.3f}, {threshold_range_short[1]:.3f}]")
    
    # Plot integrated results
    fig, axes = plt.subplots(4, 1, figsize=(15, 12))
    
    # Signals and thresholds
    axes[0].plot(df['raw_signal'], label='Raw signal', alpha=0.5, color='gray')
    axes[0].plot(df['smoothed_signal'], label='Smoothed signal', color='blue')
    axes[0].plot(df['long_threshold'], label='Long threshold', color='green', linestyle='--')
    axes[0].plot(df['short_threshold'], label='Short threshold', color='red', linestyle='--')
    axes[0].scatter(df[df['long_entry']].index, df[df['long_entry']]['smoothed_signal'], 
                   color='green', marker='^', s=50, label='Long entries')
    axes[0].scatter(df[df['short_entry']].index, df[df['short_entry']]['smoothed_signal'], 
                   color='red', marker='v', s=50, label='Short entries')
    axes[0].set_title('Signals and Adaptive Thresholds')
    axes[0].legend()
    axes[0].grid(True)
    
    # Confidence
    axes[1].plot(df['confidence'], label='Signal confidence', color='orange')
    axes[1].set_title('Signal Confidence')
    axes[1].set_ylabel('Confidence')
    axes[1].legend()
    axes[1].grid(True)
    
    # ATR ratio
    axes[2].plot(df['atr_ratio'], label='ATR ratio', color='purple')
    axes[2].set_title('Market Volatility (ATR Ratio)')
    axes[2].set_ylabel('ATR Ratio')
    axes[2].legend()
    axes[2].grid(True)
    
    # Threshold adaptation
    axes[3].plot(df['long_threshold'] - tuner.base_long_threshold, label='Long threshold adjustment', color='green')
    axes[3].plot(df['short_threshold'] - tuner.base_short_threshold, label='Short threshold adjustment', color='red')
    axes[3].set_title('Threshold Adjustments from Base')
    axes[3].set_ylabel('Adjustment')
    axes[3].set_xlabel('Time Step')
    axes[3].legend()
    axes[3].grid(True)
    
    plt.tight_layout()
    plt.savefig('integrated_adaptive_system.png', dpi=150, bbox_inches='tight')
    log.info("📊 Integrated system plot saved as 'integrated_adaptive_system.png'")
    
    return df

def main():
    """Main test function"""
    
    log.info("🚀 Starting adaptive signal processing tests...")
    
    try:
        # Test 1: Smoothing methods
        smoothing_results = test_smoothing_methods()
        
        # Test 2: Adaptive thresholds
        threshold_tuner = test_adaptive_thresholds()
        
        # Test 3: Integrated system
        integrated_results = test_integrated_system()
        
        log.info("✅ All tests completed successfully!")
        log.info("📁 Check the generated PNG files for visual results")
        
        return {
            'smoothing_results': smoothing_results,
            'threshold_tuner': threshold_tuner,
            'integrated_results': integrated_results
        }
        
    except Exception as e:
        log.error(f"❌ Test failed: {e}", exc_info=True)
        return None

if __name__ == "__main__":
    results = main()
