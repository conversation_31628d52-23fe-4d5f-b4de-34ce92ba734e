# -*- coding: utf-8 -*-
"""
fill_missing_ohlcv_binance.py
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Helper script to **back‑fill missing OHLCV arrays** in the daily *.msgpack* files
produced by your CoinAPI collector.

⚠️  **NEW:** You can now point it at a _single file_ via `--file` (or positional
    argument) – exactly what you asked for:

```bash
python fill_missing_ohlcv_binance.py \
       --file /Users/<USER>/Projects/scalpel_new/daily_combined_data_coinapi/2025-07-14.msgpack \
       --symbol XRPUSDC
```

If `--file` / positional argument is omitted, it still scans an entire directory.

Supported intervals remain unchanged; see the long docstring for details.
"""

from __future__ import annotations

import argparse
import logging
import os
import time
from collections import defaultdict
from datetime import datetime, timezone
from typing import Dict, List, Tuple

import msgpack  # type: ignore
import requests

BINANCE_BASE = "https://fapi.binance.com/fapi/v1/klines"  # USD‑M Futures
BINANCE_LIMIT = 1000  # max klines per request

# -----------------------------------------------------------------------------
# Argument parsing & logging
# -----------------------------------------------------------------------------

def parse_arguments() -> argparse.Namespace:
    p = argparse.ArgumentParser(
        description="Back‑fill missing OHLCV arrays inside daily .msgpack files by pulling klines from Binance Futures."
    )

    p.add_argument(
        "target",
        nargs="?",
        help="Optional path to SINGLE .msgpack file. If omitted, we scan --data-dir.",
    )
    p.add_argument(
        "--file",  # alias for target for clarity
        dest="file",
        help="Full path to SINGLE .msgpack file to patch (overrides --data-dir).",
    )
    p.add_argument(
        "--data-dir",
        default="daily_combined_data_coinapi",
        help="Directory containing .msgpack files (ignored when --file is used).",
    )
    p.add_argument(
        "--symbol", default="XRPUSDC", help="Binance Futures symbol (e.g. BTCUSDT, ETHUSDT, XRPUSDC)"
    )
    p.add_argument(
        "--min-sleep", type=int, default=200, help="Minimum sleep between HTTP calls in milliseconds."
    )
    p.add_argument(
        "--verbose", "-v", action="count", default=0, help="Increase logging verbosity (‑v, ‑vv)."
    )
    return p.parse_args()


def setup_logging(verbosity: int) -> None:
    level = logging.WARNING if verbosity == 0 else logging.INFO if verbosity == 1 else logging.DEBUG
    logging.basicConfig(
        format="%(asctime)s [%(levelname)s] %(message)s", datefmt="%Y‑%m‑%d %H:%M:%S", level=level
    )

# -----------------------------------------------------------------------------
# Interval mapping & conversions
# -----------------------------------------------------------------------------

# CoinAPI tf -> (Binance tf, aggregate_n). aggregate_n=None => use Binance tf directly.
TF_MAP: Dict[str, Tuple[str, int | None]] = {
    "1m": ("1m", None),
    "2m": ("1m", 2),
    "3m": ("3m", None),
    "4m": ("1m", 4),
    "5m": ("5m", None),
    "6m": ("1m", 6),
    "10m": ("1m", 10),
    "15m": ("15m", None),
    "20m": ("1m", 20),
    "30m": ("30m", None),
    "1h": ("1h", None),
    "4h": ("4h", None),
    "1d": ("1d", None),
    "1w": ("1d", 7),
}

# -----------------------------------------------------------------------------
# Binance helpers
# -----------------------------------------------------------------------------

def fetch_binance_klines(symbol: str, interval: str, start_ms: int, end_ms: int, min_sleep_ms: int = 200) -> List[List]:
    """Stream klines in chunks respecting Binance's 1000‑row limit."""
    klines: List[List] = []
    pointer = start_ms
    while pointer < end_ms:
        params = {
            "symbol": symbol,
            "interval": interval,
            "startTime": pointer,
            "endTime": min(end_ms - 1, pointer + BINANCE_LIMIT * 60_000),
            "limit": BINANCE_LIMIT,
        }
        logging.debug("GET %s", params)
        resp = requests.get(BINANCE_BASE, params=params, timeout=10)
        resp.raise_for_status()
        batch = resp.json()
        if not batch:
            break
        klines.extend(batch)
        pointer = batch[-1][0] + 1  # next millisecond after last open_time
        time.sleep(min_sleep_ms / 1000.0)
    return klines


def klines_to_coinapi(kl: List[List]) -> List[Dict]:  # noqa: ANN001
    out: List[Dict] = []
    for k in kl:
        open_ms, o, h, l, c, v, close_ms = k[0], k[1], k[2], k[3], k[4], k[5], k[6]
        out.append(
            {
                "time_period_start": datetime.utcfromtimestamp(open_ms / 1000).isoformat() + "Z",
                "time_period_end": datetime.utcfromtimestamp(close_ms / 1000).isoformat() + "Z",
                "price_open": float(o),
                "price_high": float(h),
                "price_low": float(l),
                "price_close": float(c),
                "volume_traded": float(v),
            }
        )
    return out


def aggregate_by_n(src: List[List], n: int) -> List[Dict]:  # noqa: ANN001
    if n <= 1:
        raise ValueError("n must be >1 for aggregation")
    aggregated: List[Dict] = []
    for i in range(0, len(src), n):
        chunk = src[i : i + n]
        if len(chunk) < n:
            break  # ignore incomplete tail
        open_ms = chunk[0][0]
        close_ms = chunk[-1][6]
        prices = [float(x) for x in (g[1] for g in chunk)]
        highs = max(float(g[2]) for g in chunk)
        lows = min(float(g[3]) for g in chunk)
        closes = float(chunk[-1][4])
        volume = sum(float(g[5]) for g in chunk)
        aggregated.append(
            {
                "time_period_start": datetime.utcfromtimestamp(open_ms / 1000).isoformat() + "Z",
                "time_period_end": datetime.utcfromtimestamp(close_ms / 1000).isoformat() + "Z",
                "price_open": prices[0],
                "price_high": highs,
                "price_low": lows,
                "price_close": closes,
                "volume_traded": volume,
            }
        )
    return aggregated

# -----------------------------------------------------------------------------
# Core patch routine
# -----------------------------------------------------------------------------

def patch_msgpack(path: str, symbol: str, min_sleep_ms: int) -> None:
    with open(path, "rb") as fh:
        doc = msgpack.unpack(fh, raw=False)

    ohlcv = doc.setdefault("ohlcv", {})
    start_ms, end_ms = doc["time_range_ms"]
    updated = False

    for tf, (bin_tf, agg_n) in TF_MAP.items():
        if not ohlcv.get(tf):  # missing or empty list
            logging.info("%s – fetching %s for tf %s", os.path.basename(path), bin_tf, tf)
            raw = fetch_binance_klines(symbol, bin_tf, start_ms, end_ms, min_sleep_ms)
            if agg_n:
                ohlcv[tf] = aggregate_by_n(raw, agg_n)
            else:
                ohlcv[tf] = klines_to_coinapi(raw)
            updated = True
        else:
            logging.debug("%s – tf %s already ok (len=%d)", os.path.basename(path), tf, len(ohlcv[tf]))

    if updated:
        tmp = f"{path}.tmp"
        with open(tmp, "wb") as fh:
            msgpack.pack(doc, fh)
        os.replace(tmp, path)
        logging.info("✅ %s patched", os.path.basename(path))
    else:
        logging.info("%s – nothing to update", os.path.basename(path))

# -----------------------------------------------------------------------------
# Entrypoint
# -----------------------------------------------------------------------------

def main() -> None:
    args = parse_arguments()
    setup_logging(args.verbose)

    target = args.file or args.target
    if target:  # single-file mode
        if not os.path.isfile(target):
            logging.error("File not found: %s", target)
            return
        patch_msgpack(target, args.symbol.upper(), args.min_sleep)
        return

    # directory scan mode
    if not os.path.isdir(args.data_dir):
        logging.error("Directory not found: %s", args.data_dir)
        return
    files = sorted(f for f in os.listdir(args.data_dir) if f.endswith(".msgpack"))
    if not files:
        logging.warning("No .msgpack files in %s", args.data_dir)
        return

    for fname in files:
        patch_msgpack(os.path.join(args.data_dir, fname), args.symbol.upper(), args.min_sleep)
        time.sleep(args.min_sleep / 1000.0)


if __name__ == "__main__":
    from datetime import datetime  # imported here to avoid circular refs in type hints

    main()
