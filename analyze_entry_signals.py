#!/usr/bin/env python3
"""
Analyze entry signals from trading simulation to identify optimal thresholds
"""
import subprocess
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import logging
import re

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)

def extract_entry_signals_from_log(log_output):
    """Extract entry signals from simulation log output"""
    signals = []

    # Pattern to match actual entry executions
    entry_executed_pattern = r'ENTER (LONG|SHORT) ([\d\.]+) @ ([\d\.]+)'

    lines = log_output.split('\n')

    for line in lines:
        # Look for actual entry executions
        exec_match = re.search(entry_executed_pattern, line)
        if exec_match:
            direction = exec_match.group(1)
            size = float(exec_match.group(2))
            price = float(exec_match.group(3))

            signals.append({
                'direction': direction,
                'size': size,
                'price': price,
                'entry_executed': True
            })

    return pd.DataFrame(signals)

def run_simulation_with_threshold(threshold, config_file="strategyConfig_scalp_1s.json"):
    """Run simulation with specific threshold and capture results"""
    
    # Modify config temporarily
    import json
    with open(config_file, 'r') as f:
        config = json.load(f)
    
    # Update thresholds
    config['tradeParams']['entryActionThreshold'] = threshold
    config['tradeParams']['longEntryThreshold'] = threshold
    config['tradeParams']['shortEntryThreshold'] = threshold
    
    # Save temporary config
    temp_config = f"temp_config_{threshold:.2f}.json"
    with open(temp_config, 'w') as f:
        json.dump(config, f, indent=2)
    
    try:
        # Run simulation
        cmd = [
            "python", "simulate_trading_new.py",
            "--cfg", temp_config,
            "--start", "2025-07-02",
            "--end", "2025-07-02",
            "--out-trades", f"trades_{threshold:.2f}.csv",
            "--out-equity", f"equity_{threshold:.2f}.csv"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        # Clean up temp config
        Path(temp_config).unlink(missing_ok=True)
        
        if result.returncode != 0:
            log.error(f"Simulation failed for threshold {threshold}: {result.stderr}")
            return None, None, None
        
        # Extract signals from log
        signals_df = extract_entry_signals_from_log(result.stdout)
        
        # Load trades if available
        trades_file = f"trades_{threshold:.2f}.csv"
        trades_df = None
        if Path(trades_file).exists():
            trades_df = pd.read_csv(trades_file)
            Path(trades_file).unlink()  # Clean up
        
        # Load equity if available
        equity_file = f"equity_{threshold:.2f}.csv"
        equity_df = None
        if Path(equity_file).exists():
            equity_df = pd.read_csv(equity_file)
            Path(equity_file).unlink()  # Clean up
        
        return signals_df, trades_df, equity_df
        
    except Exception as e:
        log.error(f"Error running simulation with threshold {threshold}: {e}")
        # Clean up temp config
        Path(temp_config).unlink(missing_ok=True)
        return None, None, None

def analyze_threshold_performance():
    """Analyze performance across different entry thresholds"""
    
    thresholds = [0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]
    results = []
    
    log.info("🔍 Testing different entry thresholds...")
    
    for threshold in thresholds:
        log.info(f"Testing threshold: {threshold}")
        
        signals_df, trades_df, equity_df = run_simulation_with_threshold(threshold)
        
        if signals_df is not None:
            # Analyze signals
            total_signals = len(signals_df)
            executed_signals = total_signals  # All signals in our data are executed
            execution_rate = 1.0 if total_signals > 0 else 0
            
            # Analyze trades
            total_pnl = 0
            total_trades = 0
            win_rate = 0
            avg_pnl_per_trade = 0
            
            if trades_df is not None and len(trades_df) > 0:
                total_pnl = trades_df['pnl'].sum()
                total_trades = len(trades_df)
                winning_trades = len(trades_df[trades_df['pnl'] > 0])
                win_rate = winning_trades / total_trades
                avg_pnl_per_trade = total_pnl / total_trades
            
            results.append({
                'threshold': threshold,
                'total_signals': total_signals,
                'executed_signals': executed_signals,
                'execution_rate': execution_rate,
                'total_trades': total_trades,
                'total_pnl': total_pnl,
                'win_rate': win_rate,
                'avg_pnl_per_trade': avg_pnl_per_trade
            })
    
    return pd.DataFrame(results)

def create_threshold_analysis_charts(results_df):
    """Create charts analyzing threshold performance"""
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # Chart 1: Execution Rate vs Threshold
    axes[0, 0].plot(results_df['threshold'], results_df['execution_rate'], 'bo-')
    axes[0, 0].set_title('Signal Execution Rate vs Entry Threshold')
    axes[0, 0].set_xlabel('Entry Threshold')
    axes[0, 0].set_ylabel('Execution Rate')
    axes[0, 0].grid(True, alpha=0.3)
    
    # Chart 2: Total PnL vs Threshold
    axes[0, 1].plot(results_df['threshold'], results_df['total_pnl'], 'ro-')
    axes[0, 1].set_title('Total PnL vs Entry Threshold')
    axes[0, 1].set_xlabel('Entry Threshold')
    axes[0, 1].set_ylabel('Total PnL ($)')
    axes[0, 1].grid(True, alpha=0.3)
    axes[0, 1].axhline(y=0, color='black', linestyle='--', alpha=0.5)
    
    # Chart 3: Win Rate vs Threshold
    axes[1, 0].plot(results_df['threshold'], results_df['win_rate'], 'go-')
    axes[1, 0].set_title('Win Rate vs Entry Threshold')
    axes[1, 0].set_xlabel('Entry Threshold')
    axes[1, 0].set_ylabel('Win Rate')
    axes[1, 0].grid(True, alpha=0.3)
    axes[1, 0].set_ylim(0, 1)
    
    # Chart 4: Trade Count vs Threshold
    axes[1, 1].plot(results_df['threshold'], results_df['total_trades'], 'mo-')
    axes[1, 1].set_title('Trade Count vs Entry Threshold')
    axes[1, 1].set_xlabel('Entry Threshold')
    axes[1, 1].set_ylabel('Number of Trades')
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('threshold_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    log.info("📊 Threshold analysis chart saved as 'threshold_analysis.png'")

def find_optimal_threshold(results_df):
    """Find optimal threshold based on multiple criteria"""
    
    # Normalize metrics for comparison
    results_df = results_df.copy()
    
    # Higher is better: total_pnl, win_rate, avg_pnl_per_trade
    # Balanced: execution_rate (not too high, not too low)
    
    # Normalize to 0-1 scale
    for col in ['total_pnl', 'win_rate', 'avg_pnl_per_trade']:
        if results_df[col].max() != results_df[col].min():
            results_df[f'{col}_norm'] = (results_df[col] - results_df[col].min()) / (results_df[col].max() - results_df[col].min())
        else:
            results_df[f'{col}_norm'] = 0.5
    
    # Execution rate penalty for extremes (prefer 0.1-0.5 range)
    results_df['execution_rate_score'] = results_df['execution_rate'].apply(
        lambda x: 1.0 if 0.1 <= x <= 0.5 else max(0.0, 1.0 - abs(x - 0.3) * 2)
    )
    
    # Combined score
    results_df['combined_score'] = (
        results_df['total_pnl_norm'] * 0.4 +
        results_df['win_rate_norm'] * 0.3 +
        results_df['avg_pnl_per_trade_norm'] * 0.2 +
        results_df['execution_rate_score'] * 0.1
    )
    
    # Find optimal
    optimal_idx = results_df['combined_score'].idxmax()
    optimal_threshold = results_df.loc[optimal_idx, 'threshold']
    
    return optimal_threshold, results_df

def main():
    """Main analysis function"""
    log.info("🚀 Starting entry signal analysis")
    
    try:
        # Analyze threshold performance
        results_df = analyze_threshold_performance()
        
        if len(results_df) == 0:
            log.error("No results obtained from threshold analysis")
            return
        
        # Print results table
        print("\n" + "="*100)
        print("ENTRY THRESHOLD ANALYSIS RESULTS")
        print("="*100)
        print(results_df.to_string(index=False, float_format='%.3f'))
        
        # Find optimal threshold
        optimal_threshold, enhanced_results = find_optimal_threshold(results_df)
        
        print(f"\n🎯 OPTIMAL THRESHOLD: {optimal_threshold}")
        print(f"   Based on combined score considering PnL, win rate, and execution balance")
        
        # Show top 3 thresholds
        top_3 = enhanced_results.nlargest(3, 'combined_score')[['threshold', 'total_pnl', 'win_rate', 'total_trades', 'combined_score']]
        print(f"\n🏆 TOP 3 THRESHOLDS:")
        print(top_3.to_string(index=False, float_format='%.3f'))
        
        # Create analysis charts
        create_threshold_analysis_charts(results_df)
        
        # Recommendations
        print(f"\n💡 RECOMMENDATIONS:")
        best_pnl_threshold = results_df.loc[results_df['total_pnl'].idxmax(), 'threshold']
        best_winrate_threshold = results_df.loc[results_df['win_rate'].idxmax(), 'threshold']
        
        print(f"   • For maximum PnL: {best_pnl_threshold}")
        print(f"   • For maximum win rate: {best_winrate_threshold}")
        print(f"   • For balanced performance: {optimal_threshold}")
        print(f"   • Current threshold (0.7) performance rank: {(results_df['threshold'] == 0.7).idxmax() + 1}/{len(results_df)}")
        
        print("\n" + "="*100)
        
    except Exception as e:
        log.error(f"Error in analysis: {e}")
        raise

if __name__ == "__main__":
    main()
