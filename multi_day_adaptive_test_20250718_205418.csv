date,success,total_pnl,total_trades,win_rate,profit_factor,max_drawdown,threshold_breaches,long_signals,short_signals,error
2025-07-01,True,1.6614761,3.0,66.6667,16.26,-0.7531,0.0,0.0,0.0,
2025-07-02,True,-1.083725,2.0,50.0,0.14,-1.6921,0.0,0.0,0.0,
2025-07-03,False,,,,,,,,,"2025-07-18 20:53:55 [INFO] Logging nastavený na úroveň: INFO
2025-07-18 20:53:55 [INFO] <PERSON><PERSON><PERSON><PERSON><PERSON> kniž<PERSON> pre gym.spaces: gymnasium
2025-07-18 20:53:55 [INFO] Načítavam konfiguráciu z: strategyConfig_scalp_1s.json
2025-07-18 20:53:55 [INFO] Konfigurácia načítaná pre symbol: XRPUSDC
2025-07-18 20:53:55 [INFO] Načítavam VecNormalize: sac_3198976.vecnorm.pkl
2025-07-18 20:53:55 [INFO] VecNormalize úspešne načítané
2025-07-18 20:53:55 [INFO] Používam model: sac_3198976_steps.zip
2025-07-18 20:53:55 [INFO] Agent typu PopArtSAC načítaný cez PopArtSAC.
2025-07-18 20:53:55 [INFO] Maximálny potrebný lookback pre indikátory (vrátane buffera): 210 periód
2025-07-18 20:53:55 [WARNING] Nepodarilo sa presne určiť počet dní pre lookback z timeframe '1s': Nepodporovaná jednotka timeframe: s. Používam fixný počet 5 dní navyše.
2025-07-18 20:53:55 [INFO] Rozšírené obdobie načítavania dát pre zahriatie indikátorov: 2025-06-28 -> 2025-07-03
2025-07-18 20:53:55 [WARNING] Súbor pre 2025-07-03 neexistuje: parquet_processed/XRPUSDC/1s/2025-07-03.parquet
2025-07-18 20:53:55 [INFO] Našlo sa 2 súborov v rozšírenom období.
2025-07-18 20:53:55 [INFO] Spojených 50362 riadkov z rozšíreného obdobia (5m).
2025-07-18 20:53:55 [INFO] Našlo sa 2 súborov 1s dát.
2025-07-18 20:53:55 [INFO] Spojených 50362 riadkov 1s dát.
2025-07-18 20:53:55 [INFO] Očakávaných 48 features podľa configu.
2025-07-18 20:53:55 [WARNING] Missing 1 columns → filled with zeros: hmm_state_3c_volatility_5m
2025-07-18 20:53:55 [INFO] 📊 ORDER-BOOK MASKING DISABLED: Using original order-book data
2025-07-18 20:53:55 [INFO] ✅ Filtered by essential features: 50362 rows remaining
2025-07-18 20:53:55 [INFO] 📊 Filling NaN in non-essential features: {'ATR_14': 14, 'RSI_14': 14, 'EMA_9': 8, 'EMA_21': 20, 'ADX_14': 27, 'DMP_14': 14, 'DMN_14': 14, 'bollinger_bands_upper_20_2.0': 19, 'bollinger_bands_middle_20_2.0': 19, 'bollinger_bands_lower_20_2.0': 19, 'bollinger_bands_width_20_2.0': 19}
2025-07-18 20:53:55 [INFO] 🔍 DEBUG: Using EXPECTED_FEATURE_COLS: 48 features
2025-07-18 20:53:55 [INFO] 🔍 DEBUG: primary_df after selection: 48 features
2025-07-18 20:53:55 [INFO] Počet features prítomných v DataFrame: 48
2025-07-18 20:53:55 [INFO] Identifikovaný ATR stĺpec pre SL/veľkosť: ATR_14
2025-07-18 20:53:55 [INFO] Počet esenciálnych stĺpcov pre kontrolu NaN: 48
2025-07-18 20:53:55 [INFO] Orezávam dáta na pôvodný požadovaný rozsah: 2025-07-03 00:00:00+00:00 -> 2025-07-03 23:59:59.999999+00:00
2025-07-18 20:53:55 [ERROR] Chyba v dátach/konfigurácii: Po orezaní na pôvodný časový rozsah nezostali žiadne dáta. Skontrolujte dostupnosť dát a funkčnosť dropna.
Traceback (most recent call last):
  File ""/Users/<USER>/Projects/scalpel_new/simulate_trading_new.py"", line 3447, in <module>
    backtest_data = load_backtest_data(config, start_dt, end_dt,
  File ""/Users/<USER>/Projects/scalpel_new/simulate_trading_new.py"", line 600, in load_backtest_data
    raise ValueError(""Po orezaní na pôvodný časový rozsah nezostali žiadne dáta. Skontrolujte dostupnosť dát a funkčnosť dropna."")
ValueError: Po orezaní na pôvodný časový rozsah nezostali žiadne dáta. Skontrolujte dostupnosť dát a funkčnosť dropna.
"
2025-07-04,True,0.98670197,2.0,100.0,inf,-0.5905,0.0,0.0,0.0,
2025-07-05,True,-0.21962738,1.0,0.0,0.0,-1.267,0.0,0.0,0.0,
