#!/usr/bin/env python3
"""
Test script pre overenie rolling Supertrend a ADX výpočtov.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# Pridanie cesty k modulu
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from precompute_features import calculate_rolling_supertrend, calculate_rolling_adx

def create_test_data(num_rows=7200):  # 2 hodiny 1s dát
    """Vytvorí testové OHLC dáta pre 1s timeframe"""
    
    # Začiatočný čas
    start_time = datetime(2024, 1, 1, 10, 0, 0)
    timestamps = [start_time + timedelta(seconds=i) for i in range(num_rows)]
    
    # Simulácia cenového pohybu
    np.random.seed(42)
    base_price = 50000.0
    price_changes = np.random.normal(0, 0.001, num_rows)  # 0.1% volatilita
    
    # Vytvorenie close cien
    close_prices = [base_price]
    for change in price_changes[1:]:
        new_price = close_prices[-1] * (1 + change)
        close_prices.append(new_price)
    
    # Vytvorenie OHLC dát
    data = []
    for i, (ts, close) in enumerate(zip(timestamps, close_prices)):
        # Simulácia high/low okolo close
        volatility = abs(np.random.normal(0, 0.0005))  # Intrabar volatilita
        high = close * (1 + volatility)
        low = close * (1 - volatility)
        
        # Open je predchádzajúci close (okrem prvého)
        open_price = close_prices[i-1] if i > 0 else close
        
        data.append({
            'timestamp': ts,
            'open': open_price,
            'high': max(open_price, high, close),
            'low': min(open_price, low, close),
            'close': close,
            'volume': np.random.uniform(100, 1000)
        })
    
    df = pd.DataFrame(data)
    df.set_index('timestamp', inplace=True)
    return df

def test_rolling_indicators():
    """Test rolling indikátorov"""
    
    print("🧪 Testovanie rolling indikátorov...")
    
    # Vytvorenie testových dát
    print("📊 Vytváram testové dáta (2 hodiny, 1s timeframe)...")
    test_df = create_test_data(7200)
    print(f"   Vytvorených {len(test_df)} riadkov")
    print(f"   Časové rozpätie: {test_df.index.min()} - {test_df.index.max()}")
    print(f"   Close ceny: {test_df['close'].min():.2f} - {test_df['close'].max():.2f}")
    
    # Test rolling Supertrend
    print("\n🔧 Testovanie rolling Supertrend...")
    try:
        supertrend = calculate_rolling_supertrend(test_df, period=10, multiplier=3.0, window_hours=1)
        
        valid_count = supertrend.dropna().count()
        print(f"   ✅ Supertrend vypočítaný: {valid_count}/{len(test_df)} platných hodnôt")
        
        if valid_count > 0:
            print(f"   📈 Supertrend rozsah: {supertrend.min():.2f} - {supertrend.max():.2f}")
            print(f"   📊 Posledných 5 hodnôt:")
            for i in range(-5, 0):
                if i < len(supertrend) and not pd.isna(supertrend.iloc[i]):
                    close_val = test_df['close'].iloc[i]
                    st_val = supertrend.iloc[i]
                    trend = "📈 BULL" if close_val > st_val else "📉 BEAR"
                    print(f"      {test_df.index[i]}: Close={close_val:.2f}, ST={st_val:.2f} {trend}")
        
    except Exception as e:
        print(f"   ❌ Chyba pri teste Supertrend: {e}")
    
    # Test rolling ADX
    print("\n🔧 Testovanie rolling ADX...")
    try:
        adx = calculate_rolling_adx(test_df, period=14, window_hours=1)
        
        valid_count = adx.dropna().count()
        print(f"   ✅ ADX vypočítaný: {valid_count}/{len(test_df)} platných hodnôt")
        
        if valid_count > 0:
            print(f"   📈 ADX rozsah: {adx.min():.2f} - {adx.max():.2f}")
            print(f"   📊 Posledných 5 hodnôt:")
            for i in range(-5, 0):
                if i < len(adx) and not pd.isna(adx.iloc[i]):
                    adx_val = adx.iloc[i]
                    strength = "💪 STRONG" if adx_val > 25 else "😴 WEAK"
                    print(f"      {test_df.index[i]}: ADX={adx_val:.2f} {strength}")
        
    except Exception as e:
        print(f"   ❌ Chyba pri teste ADX: {e}")
    
    # Test výkonnosti
    print("\n⏱️ Test výkonnosti...")
    import time
    
    # Test na menšom datasete
    small_df = test_df.iloc[:1800]  # 30 minút
    
    start_time = time.time()
    supertrend_small = calculate_rolling_supertrend(small_df, period=10, multiplier=3.0, window_hours=1)
    supertrend_time = time.time() - start_time
    
    start_time = time.time()
    adx_small = calculate_rolling_adx(small_df, period=14, window_hours=1)
    adx_time = time.time() - start_time
    
    print(f"   Supertrend (30min): {supertrend_time:.2f}s")
    print(f"   ADX (30min): {adx_time:.2f}s")
    print(f"   Celkový čas: {supertrend_time + adx_time:.2f}s")
    
    # Odhad pre celý deň
    daily_estimate = (supertrend_time + adx_time) * (86400 / 1800)  # 24h / 30min
    print(f"   📅 Odhad pre celý deň (24h): {daily_estimate:.1f}s ({daily_estimate/60:.1f}min)")
    
    print("\n✅ Test dokončený!")

if __name__ == "__main__":
    test_rolling_indicators()
