# Multi-Symbol Live Trading Usage

## Overview

The live trading system now supports trading multiple cryptocurrency symbols simultaneously. Each symbol runs in its own thread with independent data streams and decision logic.

## Usage Examples

### 1. Single Symbol (Backward Compatible)
```bash
# Uses symbol from config file (default: XRPUSDC)
python live_trading.py

# Explicit config file
python live_trading.py --cfg strategyConfig_scalp_1s.json
```

### 2. Multiple Symbols
```bash
# Trade 3 symbols simultaneously
python live_trading.py --symbols XRPUSDC LINKUSDC ADAUSDC

# With 1s decision frequency
python live_trading.py --symbols XRPUSDC LINKUSDC ADAUSDC --use-1s-decisions

# Test mode for multiple symbols
python live_trading.py --symbols XRPUSDC LINKUSDC ADAUSDC --test-trade
```

### 3. Advanced Usage
```bash
# Multiple symbols with custom config and debug logging
python live_trading.py \
    --cfg my_config.json \
    --symbols XRPUSDC LINKUSDC ADAUSDC DOTUSDC SOLUSDC \
    --use-1s-decisions \
    --log-level DEBUG

# Test trading with multiple symbols
python live_trading.py \
    --symbols XRPUSDC LINKUSDC \
    --test-trade \
    --log-level INFO
```

## Key Features

### 🎯 **Independent Symbol Processing**
- Each symbol runs in its own thread
- Separate data streams for each symbol
- Independent trading decisions
- Isolated risk management

### 📊 **Optimized Console Logging**
- Only threshold information is logged to console
- Reduced log spam with multiple symbols
- Format: `[SYMBOL] LONG: 0.750 vs 0.500 | Price: $0.5234`
- Full trading logs written to individual files

### 🔄 **Thread Management**
- Automatic thread restart if a symbol thread dies
- Graceful shutdown with Ctrl+C
- Thread monitoring and status reporting

### ⚡ **Performance Optimized**
- Shared model and configuration
- Efficient memory usage
- Parallel processing of symbols

## Console Output Example

```
🚀 Starting live trading for 3 symbol(s): XRPUSDC, LINKUSDC, ADAUSDC
🎯 Starting trading thread for XRPUSDC
🎯 Starting trading thread for LINKUSDC  
🎯 Starting trading thread for ADAUSDC
✅ All 3 trading threads started
📊 Console will show only threshold information for each symbol
📝 Full trading logs are written to individual log files

[XRPUSDC] LONG: 0.750 vs 0.500 | Price: $0.5234
[LINKUSDC] SHORT: -0.680 vs -0.500 | Price: $11.45
[ADAUSDC] LONG: 0.820 vs 0.500 | Price: $0.3456
```

## Configuration

The system uses the same configuration file format but applies it to each symbol:

```json
{
  "symbol": "XRPUSDC",  // Overridden by --symbols argument
  "longThreshold": 0.5,
  "shortThreshold": -0.5,
  "primaryTimeframe": "5m",
  // ... rest of config applies to all symbols
}
```

## Risk Management

### Per-Symbol Risk
- Each symbol has independent position sizing
- Separate stop-loss and take-profit levels
- Individual risk limits per symbol

### Global Risk
- Total portfolio exposure across all symbols
- Shared Binance API rate limits
- Coordinated shutdown procedures

## Monitoring

### Thread Status
The main thread monitors all trading threads:
- Detects dead threads and restarts them
- Reports thread health every 30 seconds
- Handles graceful shutdown

### Logging Strategy
- **Console**: Only threshold exceedances and key events
- **Files**: Full detailed logs per symbol (if implemented)
- **Errors**: All errors logged to console with symbol prefix

## Best Practices

### 1. Start Small
```bash
# Begin with 2-3 symbols
python live_trading.py --symbols XRPUSDC LINKUSDC
```

### 2. Use Test Mode First
```bash
# Test with multiple symbols before live trading
python live_trading.py --symbols XRPUSDC LINKUSDC ADAUSDC --test-trade
```

### 3. Monitor Resource Usage
- Each symbol uses additional CPU and memory
- Monitor system resources with 5+ symbols
- Consider VPS with adequate resources

### 4. API Rate Limits
- Binance has rate limits for API calls
- More symbols = more API calls
- Monitor for rate limit warnings

## Troubleshooting

### Thread Failures
If a symbol thread dies, it will be automatically restarted:
```
⚠️ 1 trading thread(s) died: ['Trading-LINKUSDC']
🔄 Restarting trading thread for LINKUSDC
```

### Memory Usage
With many symbols, monitor memory usage:
```bash
# Check memory usage
htop
# Or
ps aux | grep live_trading
```

### API Issues
Each symbol needs API access:
- Ensure CoinAPI key has sufficient quota
- Check Binance API permissions for all symbols
- Monitor for connection issues per symbol

## Limitations

1. **Shared Configuration**: All symbols use the same trading parameters
2. **Resource Usage**: Each symbol increases CPU/memory usage
3. **API Limits**: More symbols = more API calls
4. **Complexity**: Harder to debug with multiple symbols

## Future Enhancements

- [ ] Per-symbol configuration files
- [ ] Web dashboard for monitoring all symbols
- [ ] Advanced portfolio risk management
- [ ] Symbol-specific logging files
- [ ] Performance metrics per symbol
