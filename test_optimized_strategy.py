#!/usr/bin/env python3
"""
Test script to compare original vs optimized trading strategies
"""
import subprocess
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)

def run_simulation(script_name, config_file, start_date, end_date, output_prefix):
    """Run a trading simulation and return results"""
    trades_file = f"{output_prefix}_trades.csv"
    equity_file = f"{output_prefix}_equity.csv"
    
    cmd = [
        "python", script_name,
        "--cfg", config_file,
        "--start", start_date,
        "--end", end_date,
        "--out-trades", trades_file,
        "--out-equity", equity_file
    ]
    
    log.info(f"Running: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode != 0:
            log.error(f"Simulation failed: {result.stderr}")
            return None, None
            
        # Load results
        trades_df = None
        equity_df = None
        
        if Path(trades_file).exists():
            trades_df = pd.read_csv(trades_file)
            log.info(f"Loaded {len(trades_df)} trades from {trades_file}")
        
        if Path(equity_file).exists():
            equity_df = pd.read_csv(equity_file)
            log.info(f"Loaded {len(equity_df)} equity points from {equity_file}")
            
        return trades_df, equity_df
        
    except subprocess.TimeoutExpired:
        log.error("Simulation timed out")
        return None, None
    except Exception as e:
        log.error(f"Error running simulation: {e}")
        return None, None

def analyze_results(trades_df, equity_df, name):
    """Analyze trading results"""
    if trades_df is None or len(trades_df) == 0:
        log.warning(f"No trades data for {name}")
        return {}
    
    # Basic metrics
    total_trades = len(trades_df)
    winning_trades = len(trades_df[trades_df['pnl'] > 0])
    losing_trades = len(trades_df[trades_df['pnl'] < 0])
    win_rate = winning_trades / total_trades if total_trades > 0 else 0
    
    total_pnl = trades_df['pnl'].sum()
    avg_win = trades_df[trades_df['pnl'] > 0]['pnl'].mean() if winning_trades > 0 else 0
    avg_loss = trades_df[trades_df['pnl'] < 0]['pnl'].mean() if losing_trades > 0 else 0
    
    # Risk metrics
    profit_factor = abs(avg_win * winning_trades / (avg_loss * losing_trades)) if losing_trades > 0 and avg_loss != 0 else float('inf')
    
    # Exit reason analysis
    exit_reasons = trades_df['exit_reason'].value_counts()
    
    results = {
        'name': name,
        'total_trades': total_trades,
        'winning_trades': winning_trades,
        'losing_trades': losing_trades,
        'win_rate': win_rate,
        'total_pnl': total_pnl,
        'avg_win': avg_win,
        'avg_loss': avg_loss,
        'profit_factor': profit_factor,
        'exit_reasons': exit_reasons.to_dict()
    }
    
    return results

def compare_strategies():
    """Compare original vs optimized strategies"""
    log.info("🚀 Starting strategy comparison")
    
    # Test parameters
    start_date = "2025-07-02"
    end_date = "2025-07-02"  # Single day test
    
    # Run original simulation
    log.info("📊 Running original simulation...")
    original_trades, original_equity = run_simulation(
        "simulate_trading_new.py",
        "strategyConfig_scalp_1s.json",
        start_date, end_date,
        "original"
    )
    
    # Run optimized simulation
    log.info("🎯 Running optimized simulation...")
    optimized_trades, optimized_equity = run_simulation(
        "simulate_trading_optimized.py",
        "strategyConfig_optimized.json",
        start_date, end_date,
        "optimized"
    )
    
    # Analyze results
    log.info("📈 Analyzing results...")
    
    original_results = analyze_results(original_trades, original_equity, "Original")
    optimized_results = analyze_results(optimized_trades, optimized_equity, "Optimized")
    
    # Print comparison
    print("\n" + "="*80)
    print("STRATEGY COMPARISON RESULTS")
    print("="*80)
    
    for results in [original_results, optimized_results]:
        if not results:
            continue
            
        print(f"\n{results['name']} Strategy:")
        print(f"  Total Trades: {results['total_trades']}")
        print(f"  Win Rate: {results['win_rate']:.2%}")
        print(f"  Total PnL: ${results['total_pnl']:.2f}")
        print(f"  Avg Win: ${results['avg_win']:.2f}")
        print(f"  Avg Loss: ${results['avg_loss']:.2f}")
        print(f"  Profit Factor: {results['profit_factor']:.2f}")
        print(f"  Exit Reasons: {results['exit_reasons']}")
    
    # Calculate improvements
    if original_results and optimized_results:
        print(f"\n🎯 IMPROVEMENTS:")
        
        pnl_improvement = optimized_results['total_pnl'] - original_results['total_pnl']
        print(f"  PnL Improvement: ${pnl_improvement:.2f}")
        
        win_rate_improvement = optimized_results['win_rate'] - original_results['win_rate']
        print(f"  Win Rate Improvement: {win_rate_improvement:.2%}")
        
        trade_count_change = optimized_results['total_trades'] - original_results['total_trades']
        print(f"  Trade Count Change: {trade_count_change}")
        
        if original_results['profit_factor'] != float('inf') and optimized_results['profit_factor'] != float('inf'):
            pf_improvement = optimized_results['profit_factor'] - original_results['profit_factor']
            print(f"  Profit Factor Improvement: {pf_improvement:.2f}")
    
    print("\n" + "="*80)
    
    # Create visualization if both results exist
    if original_equity is not None and optimized_equity is not None:
        create_comparison_chart(original_equity, optimized_equity)

def create_comparison_chart(original_equity, optimized_equity):
    """Create comparison chart of equity curves"""
    try:
        plt.figure(figsize=(12, 8))
        
        # Convert timestamps
        original_equity['timestamp'] = pd.to_datetime(original_equity['timestamp'])
        optimized_equity['timestamp'] = pd.to_datetime(optimized_equity['timestamp'])
        
        # Plot equity curves
        plt.subplot(2, 1, 1)
        plt.plot(original_equity['timestamp'], original_equity['equity'], 
                label='Original Strategy', color='blue', alpha=0.7)
        plt.plot(optimized_equity['timestamp'], optimized_equity['equity'], 
                label='Optimized Strategy', color='red', alpha=0.7)
        plt.title('Equity Curve Comparison')
        plt.ylabel('Equity ($)')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # Plot positions
        plt.subplot(2, 1, 2)
        plt.plot(original_equity['timestamp'], original_equity['position'], 
                label='Original Positions', color='blue', alpha=0.7, linewidth=2)
        plt.plot(optimized_equity['timestamp'], optimized_equity['position'], 
                label='Optimized Positions', color='red', alpha=0.7, linewidth=2)
        plt.title('Position Comparison')
        plt.ylabel('Position (1=Long, -1=Short, 0=Flat)')
        plt.xlabel('Time')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('strategy_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        log.info("📊 Comparison chart saved as 'strategy_comparison.png'")
        
    except Exception as e:
        log.error(f"Error creating chart: {e}")

def main():
    """Main function"""
    try:
        compare_strategies()
    except KeyboardInterrupt:
        log.info("Comparison interrupted by user")
    except Exception as e:
        log.error(f"Error in comparison: {e}")

if __name__ == "__main__":
    main()
