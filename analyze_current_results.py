#!/usr/bin/env python3
"""
Analyze current trading results and suggest improvements
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)

def analyze_trades(trades_file):
    """Analyze trades from CSV file"""
    if not Path(trades_file).exists():
        log.error(f"Trades file not found: {trades_file}")
        return None
    
    trades_df = pd.read_csv(trades_file)
    log.info(f"Loaded {len(trades_df)} trades from {trades_file}")
    
    if len(trades_df) == 0:
        log.warning("No trades found in file")
        return None
    
    # Basic analysis
    total_trades = len(trades_df)
    winning_trades = len(trades_df[trades_df['pnl'] > 0])
    losing_trades = len(trades_df[trades_df['pnl'] < 0])
    win_rate = winning_trades / total_trades if total_trades > 0 else 0
    
    total_pnl = trades_df['pnl'].sum()
    avg_win = trades_df[trades_df['pnl'] > 0]['pnl'].mean() if winning_trades > 0 else 0
    avg_loss = trades_df[trades_df['pnl'] < 0]['pnl'].mean() if losing_trades > 0 else 0
    
    # Risk metrics
    profit_factor = abs(avg_win * winning_trades / (avg_loss * losing_trades)) if losing_trades > 0 and avg_loss != 0 else float('inf')
    
    # Exit reason analysis
    exit_reasons = trades_df['exit_reason'].value_counts()
    
    # Time analysis
    trades_df['entry_time'] = pd.to_datetime(trades_df['entry_time'])
    trades_df['hour'] = trades_df['entry_time'].dt.hour
    hourly_performance = trades_df.groupby('hour')['pnl'].agg(['count', 'sum', 'mean'])
    
    # Direction analysis
    direction_performance = trades_df.groupby('direction')['pnl'].agg(['count', 'sum', 'mean'])
    
    results = {
        'total_trades': total_trades,
        'winning_trades': winning_trades,
        'losing_trades': losing_trades,
        'win_rate': win_rate,
        'total_pnl': total_pnl,
        'avg_win': avg_win,
        'avg_loss': avg_loss,
        'profit_factor': profit_factor,
        'exit_reasons': exit_reasons.to_dict(),
        'hourly_performance': hourly_performance,
        'direction_performance': direction_performance,
        'trades_df': trades_df
    }
    
    return results

def analyze_equity(equity_file):
    """Analyze equity curve from CSV file"""
    if not Path(equity_file).exists():
        log.error(f"Equity file not found: {equity_file}")
        return None
    
    equity_df = pd.read_csv(equity_file)
    log.info(f"Loaded {len(equity_df)} equity points from {equity_file}")
    
    if len(equity_df) == 0:
        log.warning("No equity data found in file")
        return None
    
    # Check if timestamp column exists, otherwise use index
    if 'timestamp' in equity_df.columns:
        equity_df['timestamp'] = pd.to_datetime(equity_df['timestamp'])
    else:
        equity_df['timestamp'] = pd.to_datetime(equity_df.index)
    
    # Calculate metrics
    initial_equity = equity_df['equity'].iloc[0]
    final_equity = equity_df['equity'].iloc[-1]
    max_equity = equity_df['equity'].max()
    min_equity = equity_df['equity'].min()
    
    # Drawdown analysis
    equity_df['running_max'] = equity_df['equity'].expanding().max()
    equity_df['drawdown'] = equity_df['equity'] - equity_df['running_max']
    equity_df['drawdown_pct'] = equity_df['drawdown'] / equity_df['running_max'] * 100
    
    max_drawdown = equity_df['drawdown'].min()
    max_drawdown_pct = equity_df['drawdown_pct'].min()
    
    results = {
        'initial_equity': initial_equity,
        'final_equity': final_equity,
        'total_return': final_equity - initial_equity,
        'total_return_pct': (final_equity - initial_equity) / initial_equity * 100,
        'max_equity': max_equity,
        'min_equity': min_equity,
        'max_drawdown': max_drawdown,
        'max_drawdown_pct': max_drawdown_pct,
        'equity_df': equity_df
    }
    
    return results

def create_analysis_charts(trades_results, equity_results):
    """Create analysis charts"""
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # Chart 1: Equity curve
    if equity_results:
        equity_df = equity_results['equity_df']
        axes[0, 0].plot(equity_df['timestamp'], equity_df['equity'], 'b-', alpha=0.7)
        axes[0, 0].set_title('Equity Curve')
        axes[0, 0].set_ylabel('Equity ($)')
        axes[0, 0].grid(True, alpha=0.3)
        axes[0, 0].tick_params(axis='x', rotation=45)
    
    # Chart 2: Drawdown
    if equity_results:
        equity_df = equity_results['equity_df']
        axes[0, 1].fill_between(equity_df['timestamp'], equity_df['drawdown'], 0, 
                               color='red', alpha=0.3)
        axes[0, 1].set_title('Drawdown')
        axes[0, 1].set_ylabel('Drawdown ($)')
        axes[0, 1].grid(True, alpha=0.3)
        axes[0, 1].tick_params(axis='x', rotation=45)
    
    # Chart 3: PnL distribution
    if trades_results:
        trades_df = trades_results['trades_df']
        axes[0, 2].hist(trades_df['pnl'], bins=20, alpha=0.7, edgecolor='black')
        axes[0, 2].axvline(x=0, color='red', linestyle='--', alpha=0.7)
        axes[0, 2].set_title('PnL Distribution')
        axes[0, 2].set_xlabel('PnL ($)')
        axes[0, 2].set_ylabel('Frequency')
        axes[0, 2].grid(True, alpha=0.3)
    
    # Chart 4: Exit reasons
    if trades_results:
        exit_reasons = trades_results['exit_reasons']
        axes[1, 0].pie(exit_reasons.values(), labels=exit_reasons.keys(), autopct='%1.1f%%')
        axes[1, 0].set_title('Exit Reasons')
    
    # Chart 5: Hourly performance
    if trades_results:
        hourly_perf = trades_results['hourly_performance']
        axes[1, 1].bar(hourly_perf.index, hourly_perf['sum'], alpha=0.7)
        axes[1, 1].set_title('Hourly PnL')
        axes[1, 1].set_xlabel('Hour of Day')
        axes[1, 1].set_ylabel('Total PnL ($)')
        axes[1, 1].grid(True, alpha=0.3)
        axes[1, 1].axhline(y=0, color='red', linestyle='--', alpha=0.7)
    
    # Chart 6: Direction performance
    if trades_results:
        direction_perf = trades_results['direction_performance']
        directions = ['LONG' if x == 1 else 'SHORT' for x in direction_perf.index]
        axes[1, 2].bar(directions, direction_perf['sum'], alpha=0.7)
        axes[1, 2].set_title('Direction Performance')
        axes[1, 2].set_xlabel('Direction')
        axes[1, 2].set_ylabel('Total PnL ($)')
        axes[1, 2].grid(True, alpha=0.3)
        axes[1, 2].axhline(y=0, color='red', linestyle='--', alpha=0.7)
    
    plt.tight_layout()
    plt.savefig('trading_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    log.info("📊 Analysis chart saved as 'trading_analysis.png'")

def suggest_improvements(trades_results, equity_results):
    """Suggest improvements based on analysis"""
    suggestions = []
    
    if trades_results:
        win_rate = trades_results['win_rate']
        profit_factor = trades_results['profit_factor']
        avg_win = trades_results['avg_win']
        avg_loss = trades_results['avg_loss']
        exit_reasons = trades_results['exit_reasons']
        
        # Win rate analysis
        if win_rate < 0.4:
            suggestions.append(f"🔴 Low win rate ({win_rate:.1%}). Consider tightening entry criteria or improving signal quality.")
        elif win_rate > 0.6:
            suggestions.append(f"🟢 Good win rate ({win_rate:.1%}). Strategy shows promise.")
        
        # Profit factor analysis
        if profit_factor < 1.0:
            suggestions.append(f"🔴 Profit factor below 1.0 ({profit_factor:.2f}). Strategy is losing money.")
        elif profit_factor > 1.5:
            suggestions.append(f"🟢 Good profit factor ({profit_factor:.2f}). Strategy is profitable.")
        
        # Risk-reward analysis
        if abs(avg_loss) > 0:
            risk_reward = avg_win / abs(avg_loss)
            if risk_reward < 1.0:
                suggestions.append(f"🔴 Poor risk-reward ratio ({risk_reward:.2f}). Consider wider TPs or tighter SLs.")
            elif risk_reward > 1.5:
                suggestions.append(f"🟢 Good risk-reward ratio ({risk_reward:.2f}).")
        
        # Exit reason analysis
        sl_exits = exit_reasons.get('SL', 0)
        tp_exits = exit_reasons.get('TP', 0)
        total_exits = sum(exit_reasons.values())
        
        if sl_exits > tp_exits * 2:
            suggestions.append(f"🔴 Too many SL exits ({sl_exits}/{total_exits}). Consider tighter entry criteria or wider SLs.")
        
        # Hourly performance
        hourly_perf = trades_results['hourly_performance']
        best_hours = hourly_perf[hourly_perf['sum'] > 0].index.tolist()
        worst_hours = hourly_perf[hourly_perf['sum'] < 0].index.tolist()
        
        if best_hours:
            suggestions.append(f"🟢 Best performing hours: {best_hours}. Consider focusing trading during these times.")
        if worst_hours:
            suggestions.append(f"🔴 Worst performing hours: {worst_hours}. Consider avoiding trading during these times.")
        
        # Direction bias
        direction_perf = trades_results['direction_performance']
        if len(direction_perf) > 1:
            long_pnl = direction_perf.loc[direction_perf.index == 1, 'sum'].iloc[0] if 1 in direction_perf.index else 0
            short_pnl = direction_perf.loc[direction_perf.index == -1, 'sum'].iloc[0] if -1 in direction_perf.index else 0
            
            if abs(long_pnl - short_pnl) > max(abs(long_pnl), abs(short_pnl)) * 0.5:
                better_direction = "LONG" if long_pnl > short_pnl else "SHORT"
                suggestions.append(f"🔍 Strong directional bias: {better_direction} trades perform better. Consider adjusting strategy.")
    
    if equity_results:
        max_dd_pct = equity_results['max_drawdown_pct']
        
        if max_dd_pct < -10:
            suggestions.append(f"🔴 High maximum drawdown ({max_dd_pct:.1f}%). Consider reducing position sizes or improving risk management.")
        elif max_dd_pct > -5:
            suggestions.append(f"🟢 Low maximum drawdown ({max_dd_pct:.1f}%). Good risk management.")
    
    return suggestions

def main():
    """Main analysis function"""
    log.info("🚀 Starting trading results analysis")
    
    # Analyze current results
    trades_results = analyze_trades("test_trades_fixed.csv")
    equity_results = analyze_equity("test_equity_fixed.csv")
    
    if not trades_results and not equity_results:
        log.error("No data found to analyze")
        return
    
    # Print results
    print("\n" + "="*80)
    print("TRADING RESULTS ANALYSIS")
    print("="*80)
    
    if trades_results:
        print(f"\n📊 TRADE STATISTICS:")
        print(f"   Total Trades: {trades_results['total_trades']}")
        print(f"   Win Rate: {trades_results['win_rate']:.1%}")
        print(f"   Total PnL: ${trades_results['total_pnl']:.2f}")
        print(f"   Average Win: ${trades_results['avg_win']:.2f}")
        print(f"   Average Loss: ${trades_results['avg_loss']:.2f}")
        print(f"   Profit Factor: {trades_results['profit_factor']:.2f}")
        print(f"   Exit Reasons: {trades_results['exit_reasons']}")
    
    if equity_results:
        print(f"\n💰 EQUITY STATISTICS:")
        print(f"   Initial Equity: ${equity_results['initial_equity']:.2f}")
        print(f"   Final Equity: ${equity_results['final_equity']:.2f}")
        print(f"   Total Return: ${equity_results['total_return']:.2f} ({equity_results['total_return_pct']:.2f}%)")
        print(f"   Max Drawdown: ${equity_results['max_drawdown']:.2f} ({equity_results['max_drawdown_pct']:.2f}%)")
    
    # Generate suggestions
    suggestions = suggest_improvements(trades_results, equity_results)
    
    print(f"\n💡 IMPROVEMENT SUGGESTIONS:")
    for suggestion in suggestions:
        print(f"   {suggestion}")
    
    # Create charts
    create_analysis_charts(trades_results, equity_results)
    
    print("\n" + "="*80)

if __name__ == "__main__":
    main()
