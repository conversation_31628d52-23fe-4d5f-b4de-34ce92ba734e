#!/usr/bin/env python3
"""
Prepare historical data for live trading.

This script:
1. Downloads raw historical data from CoinAPI to parquet_raw
2. Computes features using precompute_features.py
3. Prepares data for live trading system

Usage:
    python prepare_historical_data.py --symbol XRPUSDC --days 7
    python prepare_historical_data.py --symbol XRPUSDC --start 2025-07-10 --end 2025-07-18
"""

import argparse
import json
import subprocess
import sys
from datetime import datetime, timezone, timedelta
from pathlib import Path
import requests
import pandas as pd
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)

def download_raw_data(symbol: str, start_date: datetime, end_date: datetime, timeframe: str = '5m') -> bool:
    """
    Download raw OHLCV data from CoinAPI and save to parquet_raw directory.
    
    Args:
        symbol: Trading symbol (e.g., 'XRPUSDC')
        start_date: Start date for data
        end_date: End date for data
        timeframe: Timeframe (e.g., '5m')
        
    Returns:
        True if successful, False otherwise
    """
    try:
        # Create output directory
        raw_dir = Path('parquet_raw') / symbol / timeframe
        raw_dir.mkdir(parents=True, exist_ok=True)
        
        # Convert timeframe to CoinAPI period_id
        period_map = {
            '1m': '1MIN',
            '5m': '5MIN',
            '15m': '15MIN',
            '1h': '1HRS',
            '4h': '4HRS',
            '1d': '1DAY'
        }
        period_id = period_map.get(timeframe, '5MIN')
        
        # CoinAPI symbol format - use exact format from coinapi.py
        if symbol == 'XRPUSDC':
            coinapi_symbol = "BINANCEFTS_PERP_XRP_USDC"
        elif symbol == 'SOLUSDC':
            coinapi_symbol = "BINANCEFTS_PERP_SOL_USDC"
        else:
            # Fallback to generic format
            coinapi_symbol = f"BINANCEFTS_PERP_{symbol.replace('USDC', '_USDC').replace('USDT', '_USDT')}"
        
        # CoinAPI REST endpoint
        url = "https://rest.coinapi.io/v1/ohlcv/history"
        headers = {
            "X-CoinAPI-Key": "a49bb33b-6504-4776-acc2-25c1026b1cef"
        }
        
        log.info(f"📥 Downloading raw data for {symbol} {timeframe} from {start_date.date()} to {end_date.date()}")
        
        # Download data day by day
        current_date = start_date.date()
        while current_date <= end_date.date():
            day_file = raw_dir / f"{current_date}.parquet"
            
            if day_file.exists():
                log.info(f"   ✅ {current_date} already exists, skipping")
                current_date += timedelta(days=1)
                continue
                
            # Calculate day start and end
            day_start = datetime.combine(current_date, datetime.min.time()).replace(tzinfo=timezone.utc)
            day_end = day_start + timedelta(days=1)
            
            params = {
                "symbol_id": coinapi_symbol,
                "period_id": period_id,
                "time_start": day_start.isoformat(),
                "time_end": day_end.isoformat(),
                "limit": 2000  # Max bars per day
            }
            
            log.info(f"   ⬇️ Downloading {current_date}...")
            
            response = requests.get(url, headers=headers, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            if not data:
                log.warning(f"   ❌ No data for {current_date}")
                current_date += timedelta(days=1)
                continue
                
            # Convert to DataFrame
            rows = []
            for bar in data:
                timestamp = pd.to_datetime(bar['time_period_start'], utc=True)
                rows.append({
                    'timestamp': timestamp,
                    'open': float(bar['price_open']),
                    'high': float(bar['price_high']),
                    'low': float(bar['price_low']),
                    'close': float(bar['price_close']),
                    'volume': float(bar['volume_traded'])
                })
                
            if rows:
                df = pd.DataFrame(rows)
                df.set_index('timestamp', inplace=True)
                df.to_parquet(day_file)
                log.info(f"   ✅ Saved {len(df)} bars to {day_file}")
            else:
                log.warning(f"   ❌ No valid data for {current_date}")
                
            current_date += timedelta(days=1)
            
        return True
        
    except Exception as e:
        log.error(f"Error downloading raw data: {e}")
        return False

def compute_features(config_file: str, start_date: datetime, end_date: datetime) -> bool:
    """
    Run precompute_features.py to compute indicators.
    
    Args:
        config_file: Configuration file path
        start_date: Start date
        end_date: End date
        
    Returns:
        True if successful, False otherwise
    """
    try:
        start_str = start_date.strftime('%Y-%m-%d')
        end_str = end_date.strftime('%Y-%m-%d')
        
        cmd = [
            'python', 'precompute_features.py',
            '--config', config_file,
            '--raw-data-dir', 'parquet_raw',
            '--output-dir', 'parquet_processed',
            '--start', start_str,
            '--end', end_str
        ]
        
        log.info(f"🔧 Computing features: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            log.info("✅ Features computed successfully")
            return True
        else:
            log.error(f"❌ Feature computation failed:")
            log.error(f"STDOUT: {result.stdout}")
            log.error(f"STDERR: {result.stderr}")
            return False
            
    except Exception as e:
        log.error(f"Error computing features: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Prepare historical data for live trading')
    parser.add_argument('--symbol', required=True, help='Trading symbol (e.g., XRPUSDC)')
    parser.add_argument('--days', type=int, help='Number of days back from today')
    parser.add_argument('--start', help='Start date (YYYY-MM-DD)')
    parser.add_argument('--end', help='End date (YYYY-MM-DD)')
    parser.add_argument('--config', default='strategyConfig_scalp_1s.json', help='Strategy config file')
    parser.add_argument('--timeframe', default='5m', help='Timeframe (default: 5m)')
    
    args = parser.parse_args()
    
    # Determine date range
    if args.days:
        end_date = datetime.now(timezone.utc)
        start_date = end_date - timedelta(days=args.days)
    elif args.start and args.end:
        start_date = datetime.strptime(args.start, '%Y-%m-%d').replace(tzinfo=timezone.utc)
        end_date = datetime.strptime(args.end, '%Y-%m-%d').replace(tzinfo=timezone.utc)
    else:
        # Default: last 7 days
        end_date = datetime.now(timezone.utc)
        start_date = end_date - timedelta(days=7)
        
    log.info(f"🚀 Preparing historical data for {args.symbol}")
    log.info(f"   Date range: {start_date.date()} to {end_date.date()}")
    log.info(f"   Timeframe: {args.timeframe}")
    log.info(f"   Config: {args.config}")
    
    # Step 1: Download raw data
    log.info("📥 Step 1: Downloading raw data from CoinAPI...")
    if not download_raw_data(args.symbol, start_date, end_date, args.timeframe):
        log.error("❌ Failed to download raw data")
        sys.exit(1)
        
    # Step 2: Compute features
    log.info("🔧 Step 2: Computing features...")
    if not compute_features(args.config, start_date, end_date):
        log.error("❌ Failed to compute features")
        sys.exit(1)
        
    log.info("🎉 Historical data preparation completed successfully!")
    log.info("   You can now start live trading with:")
    log.info(f"   python live_trading.py --cfg {args.config} --symbols {args.symbol}")

if __name__ == '__main__':
    main()
