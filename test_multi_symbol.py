#!/usr/bin/env python3
"""
Test script for multi-symbol live trading functionality
"""

import subprocess
import sys
import time
from pathlib import Path

def test_multi_symbol_help():
    """Test that the --symbols argument is available"""
    print("🧪 Testing --symbols argument availability...")
    
    result = subprocess.run([
        sys.executable, "live_trading.py", "--help"
    ], capture_output=True, text=True)
    
    if result.returncode == 0:
        if "--symbols" in result.stdout:
            print("✅ --symbols argument is available")
            print("📋 Help output shows:")
            for line in result.stdout.split('\n'):
                if 'symbols' in line.lower():
                    print(f"   {line.strip()}")
            return True
        else:
            print("❌ --symbols argument not found in help")
            return False
    else:
        print(f"❌ Error running help: {result.stderr}")
        return False

def test_multi_symbol_syntax():
    """Test that multi-symbol syntax is accepted"""
    print("\n🧪 Testing multi-symbol syntax validation...")
    
    # Test with multiple symbols (should not crash immediately)
    result = subprocess.run([
        sys.executable, "live_trading.py", 
        "--symbols", "XRPUSDC", "LINKUSDC", "ADAUSDC",
        "--test-trade"  # Use test mode to avoid real trading
    ], capture_output=True, text=True, timeout=10)
    
    # We expect it to fail due to missing config/API keys, but not due to argument parsing
    if "unrecognized arguments" in result.stderr:
        print("❌ Argument parsing failed")
        print(f"Error: {result.stderr}")
        return False
    elif "symbols from command line" in result.stdout or "symbols from command line" in result.stderr:
        print("✅ Multi-symbol syntax accepted")
        return True
    else:
        print("⚠️ Could not determine if syntax was accepted")
        print(f"stdout: {result.stdout[:200]}...")
        print(f"stderr: {result.stderr[:200]}...")
        return True  # Assume it's OK if no parsing error

def test_config_compatibility():
    """Test that the script still works with existing config"""
    print("\n🧪 Testing backward compatibility with existing config...")
    
    # Check if config file exists
    config_file = Path("strategyConfig_scalp_1s.json")
    if not config_file.exists():
        print("⚠️ Config file not found, skipping compatibility test")
        return True
    
    # Test without --symbols (should use config default)
    result = subprocess.run([
        sys.executable, "live_trading.py", 
        "--test-trade"  # Use test mode
    ], capture_output=True, text=True, timeout=10)
    
    if "Using single symbol from config" in result.stdout or "Using single symbol from config" in result.stderr:
        print("✅ Backward compatibility maintained")
        return True
    else:
        print("⚠️ Could not verify backward compatibility")
        return True  # Assume it's OK

def main():
    """Run all tests"""
    print("🚀 Testing multi-symbol live trading functionality\n")
    
    tests = [
        ("Help argument test", test_multi_symbol_help),
        ("Multi-symbol syntax test", test_multi_symbol_syntax),
        ("Config compatibility test", test_config_compatibility),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except subprocess.TimeoutExpired:
            print(f"⏰ {test_name} timed out (expected for live trading)")
            results.append((test_name, True))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*50)
    print("📊 TEST SUMMARY")
    print("="*50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Results: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Multi-symbol functionality is ready.")
    else:
        print("⚠️ Some tests failed. Check the implementation.")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
