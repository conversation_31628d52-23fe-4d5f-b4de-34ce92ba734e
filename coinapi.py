import os
import sys
import time
import json
import msgpack
import requests
from datetime import datetime, timezone, timedelta, date
from collections import defaultdict
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get API key from environment (no hardcoded fallback)
COINAPI_KEY = '0a2a4f18-6551-4ab4-a3d8-4f785cbca47e'
if not COINAPI_KEY:
    print("❌ ERROR: COINAPI_KEY not found in environment variables!")
    print("❌ Please set COINAPI_KEY in .env file")
    sys.exit(1)
BASE_URL = "https://rest.coinapi.io/v1/"
API_LIMIT = 50000
RATE_LIMIT_SLEEP = 60
REQUEST_DELAY = 0.1
SYMBOL_COINAPI = "BINANCEFTS_PERP_XRP_USDC"
DESIRED_TIMEFRAMES = [
    '1s','1m','2m','3m','4m','5m','6m','10m','15m','20m','30m','1h','4h','1d','1w'
]
DATE_RANGES = [
    ('2025-05-19T00:00:00Z','2025-05-23T00:00:00Z'),
]
OUTPUT_DIR = "daily_combined_data_coinapi"
API_KEYS_FILE = "api_keys.txt"
DAYS_PER_KEY = 3

def parse_iso_datetime(t):
    if not isinstance(t, str):
        return None
    original_t = t
    try:
        if t.endswith('Z'):
            t = t[:-1] + '+00:00'
        if '.' in t:
            parts = t.split('.')
            if len(parts) > 1:
                micros_part = parts[1].split('+')[0].split('-')[0]
                if len(micros_part) > 6:
                    micros_part = micros_part[:6]
                if '+' in parts[1]:
                    timezone_part = parts[1].split('+')[1]
                    t = parts[0] + '.' + micros_part + '+' + timezone_part
                elif '-' in parts[1]:
                    timezone_part = parts[1].split('-')[1]
                    t = parts[0] + '.' + micros_part + '-' + timezone_part
                else:
                    t = parts[0] + '.' + micros_part + '+00:00'
        d = datetime.fromisoformat(t)
        if d.tzinfo is None or d.tzinfo.utcoffset(d) is None:
            d = d.replace(tzinfo=timezone.utc)
        else:
            d = d.astimezone(timezone.utc)
        return d
    except:
        return None

def map_timeframe_to_coinapi(tf):
    unit_map = {'s': 'SEC', 'm': 'MIN', 'h': 'HRS', 'd': 'DAY', 'w': 'DAY'}
    try:
        unit = tf[-1]
        value = int(tf[:-1])
        if unit == 'w':
            value *= 7
            unit = 'd'
        return f"{value}{unit_map[unit]}"
    except:
        return None

def make_coinapi_request(endpoint, params, api_key):
    headers = {'X-CoinAPI-Key': api_key}
    url = f"{BASE_URL}{endpoint}"
    retries = 3
    for attempt in range(retries):
        try:
            response = requests.get(url, headers=headers, params=params)
            if response.status_code == 429:
                print(f"  Chyba 429 - Rate limit. Čakám {RATE_LIMIT_SLEEP} sekúnd... (Pokus {attempt+1}/{retries})")
                time.sleep(RATE_LIMIT_SLEEP)
                continue
            response.raise_for_status()
            return response.json()
        except requests.exceptions.HTTPError as err:
            print(f"  HTTP Chyba ({err.response.status_code}) pri volaní {endpoint}: {err}")
            print(f"  Response Body: {err.response.text}")
            if attempt == retries - 1:
                print("  Maximálny počet pokusov dosiahnutý.")
                return None
            time.sleep(5 * (attempt + 1))
        except requests.exceptions.RequestException as err:
            print(f"  Chyba pripojenia k CoinAPI ({endpoint}): {err} (Pokus {attempt+1}/{retries})")
            if attempt == retries - 1:
                print("  Maximálny počet pokusov dosiahnutý.")
                return None
            time.sleep(5 * (attempt + 1))
        except json.JSONDecodeError as err:
            print(f"  Chyba pri spracovaní JSON odpovede ({endpoint}): {err}")
            print(f"  Response Text: {response.text if 'response' in locals() else 'N/A'}")
            return None
        except:
            return None
    return None

def fetch_historical_data(data_type, symbol, api_key, start_iso, end_iso, extra_params=None):
    if not api_key:
        return []
    endpoint_map = {
        'orderbooks': f"orderbooks/{symbol}/history",
        'trades': f"trades/{symbol}/history",
        'ohlcv': f"ohlcv/{symbol}/history"
    }
    if data_type not in endpoint_map:
        return []
    endpoint = endpoint_map[data_type]
    print(f"[CoinAPI] Sťahujem {data_type} pre {symbol} od {start_iso} do {end_iso}" + (f" ({extra_params})" if extra_params else ""))
    results = []
    current_start_iso = start_iso
    end_dt = parse_iso_datetime(end_iso)
    total_fetched = 0
    while True:
        current_start_dt = parse_iso_datetime(current_start_iso)
        if not current_start_dt:
            break
        if end_dt and current_start_dt >= end_dt:
            print(f"  Štartovací čas ({current_start_iso}) je za alebo rovný koncovému ({end_iso}). Končím fetch pre tento interval.")
            break
        params = {'time_start': current_start_iso, 'time_end': end_iso, 'limit': API_LIMIT}
        if extra_params:
            params.update(extra_params)
        print(f"  Sťahujem od {current_start_iso} (limit {API_LIMIT})... ", end="")
        sys.stdout.flush()
        data = make_coinapi_request(endpoint, params, api_key)
        if data is None:
            print(f"Chyba pri sťahovaní dát pre interval od {current_start_iso}. Fetch pre tento interval končí.")
            break
        if not data:
            print("Žiadne ďalšie dáta.")
            break
        results.extend(data)
        num_fetched = len(data)
        total_fetched += num_fetched
        time_key = 'time_exchange' if data_type in ['orderbooks', 'trades'] else 'time_period_end'
        if time_key not in data[-1]:
            print(f"  Chýba kľúč '{time_key}' v poslednom zázname. Končím fetch.")
            break
        last_time_str = data[-1][time_key]
        print(f"OK, +{num_fetched} záznamov. Posledný čas ({time_key}): {last_time_str}. Celkovo: {total_fetched}")
        last_dt = parse_iso_datetime(last_time_str)
        if not last_dt:
            print("  Chyba parsovania posledného času, končím.")
            break
        if data_type == 'ohlcv':
            next_start_dt = last_dt
        else:
            next_start_dt = last_dt + timedelta(seconds=1)
        current_start_iso = next_start_dt.isoformat(timespec='seconds').replace('+00:00', 'Z')
        if end_dt and next_start_dt >= end_dt:
            print("  Dosiahli sme alebo prekročili koncový čas. Končím fetch.")
            break
        # Removed problematic early termination - continue fetching until end time is reached
        # The proper termination condition is already handled above: "if end_dt and next_start_dt >= end_dt"
        time.sleep(REQUEST_DELAY)
    print(f"[CoinAPI] Sťahovanie {data_type} pre {start_iso} až {end_iso} dokončené. Celkovo stiahnutých: {len(results)} záznamov.\n")
    return results

def load_api_keys():
    """Load API keys from the API_KEYS_FILE."""
    keys = []
    try:
        if os.path.exists(API_KEYS_FILE):
            with open(API_KEYS_FILE, 'r') as f:
                keys = [line.strip() for line in f if line.strip()]
            print(f"Načítaných {len(keys)} API kľúčov z {API_KEYS_FILE}")
        else:
            print(f"Súbor s API kľúčmi {API_KEYS_FILE} neexistuje, použijem predvolený kľúč")
    except Exception as e:
        print(f"Chyba pri načítaní API kľúčov: {e}")

    # Add default key if no keys were loaded
    if not keys:
        keys.append(COINAPI_KEY)

    return keys

def main():
    # Load all API keys
    api_keys = load_api_keys()
    current_key_index = 0
    days_processed_with_current_key = 0

    # Use hardcoded API key directly from script
    print(f"Používam API kľúč priamo zo scriptu")

    if not api_keys or 'YOUR_API_KEY' in api_keys[0]:
        print("Chyba: Chýba CoinAPI kľúč.")
        return

    if not os.path.exists(OUTPUT_DIR):
        try:
            os.makedirs(OUTPUT_DIR)
            print(f"Vytvorený výstupný priečinok: {OUTPUT_DIR}")
        except OSError as e:
            print(f"Chyba: Nepodarilo sa vytvoriť priečinok '{OUTPUT_DIR}': {e}")
            return

    print("="*60)
    print(" Začínam sťahovanie a ukladanie denných dát cez CoinAPI")
    print(f" CoinAPI Symbol: {SYMBOL_COINAPI}")
    print(f" Požadované Timeframes: {', '.join(DESIRED_TIMEFRAMES)}")
    print(f" Výstupný Priečinok: {OUTPUT_DIR}")
    print(f" Počet API kľúčov: {len(api_keys)}")
    print(f" Dní na jeden kľúč: {DAYS_PER_KEY}")
    print("="*60)

    for range_start_iso, range_end_iso in DATE_RANGES:
        print(f"\n--- Spracovávam Časový Rozsah: {range_start_iso} až {range_end_iso} ---")
        range_start_dt = parse_iso_datetime(range_start_iso)
        range_end_dt = parse_iso_datetime(range_end_iso)
        if not range_start_dt or not range_end_dt:
            print("Chyba: Neplatný formát dátumu.")
            continue
        if range_start_dt >= range_end_dt:
            print("Chyba: Štartovací dátum je neskôr alebo rovný koncovému.")
            continue

        current_day_start_dt = datetime(range_start_dt.year, range_start_dt.month, range_start_dt.day, tzinfo=timezone.utc)
        while current_day_start_dt < range_end_dt:
            # Check if we need to switch to the next API key
            if days_processed_with_current_key >= DAYS_PER_KEY:
                current_key_index = (current_key_index + 1) % len(api_keys)
                days_processed_with_current_key = 0
                print(f"\n>>> Prepínam na ďalší API kľúč (index: {current_key_index}) <<<")

            api_key_to_use = api_keys[current_key_index]

            day_date_str = current_day_start_dt.strftime('%Y-%m-%d')
            print(f"\n>>> Spracovávam Deň: {day_date_str} (API kľúč {current_key_index + 1}/{len(api_keys)}, deň {days_processed_with_current_key + 1}/{DAYS_PER_KEY}) <<<")
            next_day_start_dt = current_day_start_dt + timedelta(days=1)
            day_end_dt = min(next_day_start_dt, range_end_dt)
            day_start_iso = current_day_start_dt.isoformat(timespec='seconds').replace('+00:00', 'Z')
            day_end_iso = day_end_dt.isoformat(timespec='seconds').replace('+00:00', 'Z')
            day_start_ms = int(current_day_start_dt.timestamp() * 1000)
            day_end_ms = int(day_end_dt.timestamp() * 1000)

            daily_orderbooks = fetch_historical_data('orderbooks', SYMBOL_COINAPI, api_key_to_use, day_start_iso, day_end_iso)
            daily_trades_raw = fetch_historical_data('trades', SYMBOL_COINAPI, api_key_to_use, day_start_iso, day_end_iso)

            parsed_trades = []
            for t in daily_trades_raw:
                side = t.get('taker_side')
                if side == 'BUY':
                    t['is_buyer_maker'] = False
                elif side == 'SELL':
                    t['is_buyer_maker'] = True
                else:
                    t['is_buyer_maker'] = None
                dt_parsed = parse_iso_datetime(t.get('time_exchange'))
                price = t.get('price') or 0
                size = t.get('size') or 0
                parsed_trades.append((dt_parsed, side, price, size))

            daily_ohlcv = {}
            for tf in DESIRED_TIMEFRAMES:
                coinapi_period_id = map_timeframe_to_coinapi(tf)
                if not coinapi_period_id:
                    continue
                print(f"  Sťahujem OHLCV pre timeframe {tf} ({coinapi_period_id})...")
                ohlcv_params = {'period_id': coinapi_period_id}
                ohlcv_data = fetch_historical_data('ohlcv', SYMBOL_COINAPI, api_key_to_use, day_start_iso, day_end_iso, extra_params=ohlcv_params)
                if ohlcv_data is not None:
                    for c_dict in ohlcv_data:
                        start_candle = parse_iso_datetime(c_dict.get('time_period_start'))
                        end_candle = parse_iso_datetime(c_dict.get('time_period_end'))
                        if not start_candle or not end_candle:
                            c_dict['vwap'] = None
                            c_dict['buy_volume'] = None
                            c_dict['sell_volume'] = None
                            continue
                        trades_in_candle = [x for x in parsed_trades if x[0] and (start_candle <= x[0] < end_candle)]
                        if not trades_in_candle:
                            c_dict['vwap'] = None
                            c_dict['buy_volume'] = 0
                            c_dict['sell_volume'] = 0
                        else:
                            total_qty = 0
                            total_px_qty = 0
                            buy_vol = 0
                            sell_vol = 0
                            for (dt_par, s, p, sz) in trades_in_candle:
                                total_px_qty += p * sz
                                total_qty += sz
                                if s == 'BUY':
                                    buy_vol += sz
                                elif s == 'SELL':
                                    sell_vol += sz
                            c_dict['vwap'] = total_px_qty / total_qty if total_qty else None
                            c_dict['buy_volume'] = buy_vol
                            c_dict['sell_volume'] = sell_vol
                    daily_ohlcv[tf] = ohlcv_data
                    print(f"    -> Stiahnutých {len(ohlcv_data)} sviečok ({tf})")
                else:
                    daily_ohlcv[tf] = []

            output_data = {
                'date': day_date_str,
                'time_range_iso': (day_start_iso, day_end_iso),
                'time_range_ms': (day_start_ms, day_end_ms),
                'coinapi_symbol': SYMBOL_COINAPI,
                'orderbooks': daily_orderbooks,
                'trades': daily_trades_raw,
                'ohlcv': daily_ohlcv
            }

            output_filename = f"{day_date_str}.msgpack"
            output_filepath = os.path.join(OUTPUT_DIR, output_filename)
            try:
                print(f"---> Ukladám dáta pre {day_date_str} do súboru: {output_filepath}")
                with open(output_filepath, 'wb') as f:
                    msgpack.pack(output_data, f)
                print(f"[OK] Dáta pre {day_date_str} úspešne uložené.")

                # Increment the counter for days processed with the current key
                days_processed_with_current_key += 1

            except Exception as e:
                print(f"[Chyba] Nepodarilo sa uložiť dáta pre {day_date_str}: {e}")

            current_day_start_dt = next_day_start_dt
            time.sleep(1)

    print("\n=== Spracovanie všetkých definovaných rozsahov a dní dokončené ===")

if __name__ == '__main__':
    main()
