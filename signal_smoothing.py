"""
Advanced Signal Smoothing Module for Trading Systems

This module provides various adaptive signal smoothing methods to improve
trading signal quality and reduce noise from neural network predictions.

Methods implemented:
1. EMA (Exponential Moving Average) - traditional approach
2. KAMA (Kaufman Adaptive Moving Average) - adapts to trend/noise ratio
3. Median + EMA - removes outliers then smooths
4. Kalman Filter - optimal state estimation
5. Multi-prediction ensemble - reduces model uncertainty
6. Trend confirmation - requires trend alignment before signals
"""

import numpy as np
import pandas as pd
from typing import List, Optional, Dict, Any
import logging

log = logging.getLogger(__name__)


class SignalSmoother:
    """
    Adaptive signal smoothing class that supports multiple smoothing methods
    """
    
    def __init__(self, method: str = "ema", **kwargs):
        """
        Initialize signal smoother
        
        Args:
            method: Smoothing method ("ema", "kama", "median_ema", "kalman", "multi_pred", "trend_confirm")
            **kwargs: Method-specific parameters
        """
        self.method = method.lower()
        self.kwargs = kwargs
        
        # Initialize method-specific state
        self._init_method()
        
        # Common state
        self.signal_history = []
        self.smoothed_history = []
        
    def _init_method(self):
        """Initialize method-specific parameters and state"""
        
        if self.method == "ema":
            self.alpha = self.kwargs.get('emaAlpha', 0.7)
            self.smoothed_value = None
            
        elif self.method == "kama":
            self.fast_sc = self.kwargs.get('kamaFastSC', 0.666)
            self.slow_sc = self.kwargs.get('kamaSlowSC', 0.064)
            self.lookback = self.kwargs.get('kamaLookback', 10)
            self.kama_value = None
            
        elif self.method == "median_ema":
            self.median_window = self.kwargs.get('medianWindow', 5)
            self.ema_alpha = self.kwargs.get('emaAlpha', 0.4)
            self.raw_signals = []
            self.smoothed_value = None
            
        elif self.method == "kalman":
            self.process_noise = self.kwargs.get('kalmanProcessNoise', 0.01)
            self.measurement_noise = self.kwargs.get('kalmanMeasurementNoise', 0.1)
            self.state_estimate = 0.0
            self.error_covariance = 1.0
            
        elif self.method == "multi_pred":
            self.prediction_count = self.kwargs.get('multiPredictionCount', 7)
            # This method requires external prediction generation
            
        elif self.method == "trend_confirm":
            self.ema_short_period = self.kwargs.get('trendEmaShort', 3)
            self.ema_long_period = self.kwargs.get('trendEmaLong', 8)
            self.ema_short = None
            self.ema_long = None
            self.base_alpha = self.kwargs.get('emaAlpha', 0.7)
            self.smoothed_value = None
            
        else:
            raise ValueError(f"Unknown smoothing method: {self.method}")
    
    def update(self, raw_signal: float, atr_ratio: float = 1.0, 
               multi_predictions: Optional[List[float]] = None) -> Dict[str, Any]:
        """
        Update smoother with new signal and return smoothed result
        
        Args:
            raw_signal: Raw signal from neural network
            atr_ratio: Current ATR / Average ATR for volatility adjustment
            multi_predictions: List of predictions for multi_pred method
            
        Returns:
            Dict with smoothed_signal, confidence, and method-specific metrics
        """
        
        # Store raw signal
        self.signal_history.append(raw_signal)
        if len(self.signal_history) > 50:  # Keep last 50 signals
            self.signal_history.pop(0)
        
        # Apply smoothing based on method
        if self.method == "ema":
            result = self._update_ema(raw_signal, atr_ratio)
            
        elif self.method == "kama":
            result = self._update_kama(raw_signal)
            
        elif self.method == "median_ema":
            result = self._update_median_ema(raw_signal)
            
        elif self.method == "kalman":
            result = self._update_kalman(raw_signal)
            
        elif self.method == "multi_pred":
            result = self._update_multi_pred(raw_signal, multi_predictions)
            
        elif self.method == "trend_confirm":
            result = self._update_trend_confirm(raw_signal)
            
        else:
            # Fallback to raw signal
            result = {
                'smoothed_signal': raw_signal,
                'confidence': 1.0,
                'method_info': {'method': 'raw'}
            }
        
        # Store smoothed result
        self.smoothed_history.append(result['smoothed_signal'])
        if len(self.smoothed_history) > 50:
            self.smoothed_history.pop(0)
            
        return result
    
    def _update_ema(self, raw_signal: float, atr_ratio: float) -> Dict[str, Any]:
        """EMA with optional ATR-based alpha adjustment"""
        
        # Adjust alpha based on volatility (higher volatility = more responsive)
        adjusted_alpha = self.alpha * (1 + (atr_ratio - 1) * 0.3)
        adjusted_alpha = np.clip(adjusted_alpha, 0.1, 0.9)
        
        if self.smoothed_value is None:
            self.smoothed_value = raw_signal
        else:
            self.smoothed_value = adjusted_alpha * raw_signal + (1 - adjusted_alpha) * self.smoothed_value
        
        return {
            'smoothed_signal': self.smoothed_value,
            'confidence': 1.0,
            'method_info': {
                'method': 'ema',
                'alpha': adjusted_alpha,
                'atr_ratio': atr_ratio
            }
        }
    
    def _update_kama(self, raw_signal: float) -> Dict[str, Any]:
        """Kaufman Adaptive Moving Average"""
        
        if len(self.signal_history) < self.lookback:
            # Not enough history, use EMA
            if self.kama_value is None:
                self.kama_value = raw_signal
            else:
                self.kama_value = 0.5 * raw_signal + 0.5 * self.kama_value
            
            return {
                'smoothed_signal': self.kama_value,
                'confidence': 0.5,  # Lower confidence during initialization
                'method_info': {'method': 'kama', 'efficiency_ratio': 0.5}
            }
        
        # Calculate efficiency ratio
        recent_signals = self.signal_history[-self.lookback:]
        direction = abs(recent_signals[-1] - recent_signals[0])
        volatility = sum(abs(recent_signals[i] - recent_signals[i-1]) 
                        for i in range(1, len(recent_signals)))
        
        if volatility == 0:
            efficiency_ratio = 1.0
        else:
            efficiency_ratio = direction / volatility
        
        # Calculate smoothing constant
        sc = (efficiency_ratio * (self.fast_sc - self.slow_sc) + self.slow_sc) ** 2
        
        # Update KAMA
        if self.kama_value is None:
            self.kama_value = raw_signal
        else:
            self.kama_value = self.kama_value + sc * (raw_signal - self.kama_value)
        
        return {
            'smoothed_signal': self.kama_value,
            'confidence': efficiency_ratio,
            'method_info': {
                'method': 'kama',
                'efficiency_ratio': efficiency_ratio,
                'smoothing_constant': sc
            }
        }
    
    def _update_median_ema(self, raw_signal: float) -> Dict[str, Any]:
        """Median filter followed by EMA"""
        
        # Store raw signals for median calculation
        self.raw_signals.append(raw_signal)
        if len(self.raw_signals) > self.median_window:
            self.raw_signals.pop(0)
        
        # Calculate median
        median_signal = np.median(self.raw_signals)
        
        # Apply EMA to median
        if self.smoothed_value is None:
            self.smoothed_value = median_signal
        else:
            self.smoothed_value = self.ema_alpha * median_signal + (1 - self.ema_alpha) * self.smoothed_value
        
        # Calculate confidence based on how much median differs from raw
        outlier_effect = abs(raw_signal - median_signal)
        confidence = max(0.1, 1.0 - outlier_effect)
        
        return {
            'smoothed_signal': self.smoothed_value,
            'confidence': confidence,
            'method_info': {
                'method': 'median_ema',
                'median_signal': median_signal,
                'outlier_effect': outlier_effect
            }
        }
    
    def _update_kalman(self, raw_signal: float) -> Dict[str, Any]:
        """1D Kalman filter for signal smoothing"""
        
        # Prediction step
        predicted_state = self.state_estimate
        predicted_covariance = self.error_covariance + self.process_noise
        
        # Update step
        kalman_gain = predicted_covariance / (predicted_covariance + self.measurement_noise)
        self.state_estimate = predicted_state + kalman_gain * (raw_signal - predicted_state)
        self.error_covariance = (1 - kalman_gain) * predicted_covariance
        
        # Confidence based on Kalman gain (lower gain = higher confidence)
        confidence = 1.0 - kalman_gain
        
        return {
            'smoothed_signal': self.state_estimate,
            'confidence': confidence,
            'method_info': {
                'method': 'kalman',
                'kalman_gain': kalman_gain,
                'error_covariance': self.error_covariance
            }
        }
    
    def _update_multi_pred(self, raw_signal: float, multi_predictions: Optional[List[float]]) -> Dict[str, Any]:
        """Ensemble of multiple predictions"""
        
        if multi_predictions is None or len(multi_predictions) == 0:
            # Fallback to single prediction
            return {
                'smoothed_signal': raw_signal,
                'confidence': 0.5,
                'method_info': {'method': 'multi_pred', 'prediction_count': 1}
            }
        
        # Use median to reduce outlier impact
        ensemble_signal = np.median(multi_predictions)
        
        # Confidence based on prediction agreement
        prediction_std = np.std(multi_predictions)
        confidence = max(0.1, 1.0 - prediction_std * 2)  # Lower std = higher confidence
        
        return {
            'smoothed_signal': ensemble_signal,
            'confidence': confidence,
            'method_info': {
                'method': 'multi_pred',
                'prediction_count': len(multi_predictions),
                'prediction_std': prediction_std,
                'raw_predictions': multi_predictions
            }
        }
    
    def _update_trend_confirm(self, raw_signal: float) -> Dict[str, Any]:
        """Trend confirmation using dual EMA"""
        
        # Update short and long EMAs
        alpha_short = 2.0 / (self.ema_short_period + 1)
        alpha_long = 2.0 / (self.ema_long_period + 1)
        
        if self.ema_short is None:
            self.ema_short = raw_signal
            self.ema_long = raw_signal
        else:
            self.ema_short = alpha_short * raw_signal + (1 - alpha_short) * self.ema_short
            self.ema_long = alpha_long * raw_signal + (1 - alpha_long) * self.ema_long
        
        # Check trend confirmation
        trend_confirmed = False
        if raw_signal > 0:  # Long signal
            trend_confirmed = self.ema_short > self.ema_long
        elif raw_signal < 0:  # Short signal
            trend_confirmed = self.ema_short < self.ema_long
        
        # Apply base smoothing
        if self.smoothed_value is None:
            self.smoothed_value = raw_signal
        else:
            self.smoothed_value = self.base_alpha * raw_signal + (1 - self.base_alpha) * self.smoothed_value
        
        # Reduce signal strength if trend not confirmed
        final_signal = self.smoothed_value
        if not trend_confirmed:
            final_signal *= 0.5  # Reduce signal strength
        
        confidence = 1.0 if trend_confirmed else 0.5
        
        return {
            'smoothed_signal': final_signal,
            'confidence': confidence,
            'method_info': {
                'method': 'trend_confirm',
                'trend_confirmed': trend_confirmed,
                'ema_short': self.ema_short,
                'ema_long': self.ema_long
            }
        }
    
    def get_stability_metrics(self) -> Dict[str, float]:
        """Calculate signal stability metrics"""
        
        if len(self.smoothed_history) < 3:
            return {'variance': 0.0, 'range': 0.0, 'trend_strength': 0.0}
        
        recent_signals = self.smoothed_history[-3:]
        
        variance = np.var(recent_signals)
        signal_range = max(recent_signals) - min(recent_signals)
        
        # Trend strength (how consistently signals move in one direction)
        if len(self.smoothed_history) >= 5:
            recent_5 = self.smoothed_history[-5:]
            diffs = np.diff(recent_5)
            trend_strength = abs(np.mean(diffs)) / (np.std(diffs) + 1e-8)
        else:
            trend_strength = 0.0
        
        return {
            'variance': variance,
            'range': signal_range,
            'trend_strength': trend_strength
        }
