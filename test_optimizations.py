#!/usr/bin/env python3
"""
Test script to validate the trading optimizations implemented in simulate_trading_new.py
"""

import subprocess
import sys
import json
from datetime import datetime

def run_simulation(config_file, start_date, end_date, description):
    """Run a simulation and return the results"""
    print(f"\n{'='*60}")
    print(f"Running {description}")
    print(f"Config: {config_file}")
    print(f"Period: {start_date} to {end_date}")
    print(f"{'='*60}")
    
    cmd = [
        sys.executable, "simulate_trading_new.py",
        "--cfg", config_file,
        "--start", start_date,
        "--end", end_date,
        "--out-trades", f"trades_{description.lower().replace(' ', '_')}.csv",
        "--out-equity", f"equity_{description.lower().replace(' ', '_')}.csv"
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        if result.returncode == 0:
            print("✅ Simulation completed successfully")
            # Extract key metrics from output
            lines = result.stdout.split('\n')
            for line in lines:
                if 'Finálna Equity' in line or 'Total trades' in line or 'Win rate' in line:
                    print(f"📊 {line.strip()}")
        else:
            print("❌ Simulation failed")
            print(f"Error: {result.stderr}")
        return result
    except subprocess.TimeoutExpired:
        print("⏰ Simulation timed out")
        return None

def create_baseline_config():
    """Create a baseline configuration for comparison"""
    with open('strategyConfig_scalp_1s.json', 'r') as f:
        config = json.load(f)
    
    # Create baseline with original settings
    baseline_config = config.copy()
    baseline_config['tradeParams']['entryActionThreshold'] = 0.7
    baseline_config['tradeParams']['longEntryThreshold'] = 0.7
    baseline_config['tradeParams']['shortEntryThreshold'] = 0.7
    baseline_config['tradeParams']['exitActionThreshold'] = 0.7
    baseline_config['tradeParams']['agentExitsEnabled'] = False
    baseline_config['tradeParams']['enableDynamicThresholds'] = False
    baseline_config['tradeParams']['rrTarget'] = 1.0
    baseline_config['trailingStopLoss']['enabled'] = False
    
    with open('strategyConfig_baseline.json', 'w') as f:
        json.dump(baseline_config, f, indent=2)
    
    return 'strategyConfig_baseline.json'

def main():
    print("🚀 Testing Trading Optimizations")
    print(f"Started at: {datetime.now()}")
    
    # Test periods (shorter for quick validation)
    test_periods = [
        ("2025-07-05", "2025-07-06"),  # 1 day test
        ("2025-07-01", "2025-07-02"),  # Another 1 day test
    ]
    
    # Create baseline configuration
    baseline_config = create_baseline_config()
    
    results = []
    
    for start_date, end_date in test_periods:
        # Test baseline configuration
        print(f"\n🔍 Testing period: {start_date} to {end_date}")
        
        baseline_result = run_simulation(
            baseline_config, start_date, end_date, 
            f"Baseline_{start_date}"
        )
        
        # Test optimized configuration
        optimized_result = run_simulation(
            'strategyConfig_scalp_1s.json', start_date, end_date,
            f"Optimized_{start_date}"
        )
        
        results.append({
            'period': f"{start_date} to {end_date}",
            'baseline': baseline_result,
            'optimized': optimized_result
        })
    
    print(f"\n{'='*60}")
    print("📈 OPTIMIZATION TEST SUMMARY")
    print(f"{'='*60}")
    
    for result in results:
        print(f"\nPeriod: {result['period']}")
        if result['baseline'] and result['optimized']:
            if result['baseline'].returncode == 0 and result['optimized'].returncode == 0:
                print("✅ Both simulations completed successfully")
            else:
                print("❌ One or both simulations failed")
        else:
            print("⏰ One or both simulations timed out")
    
    print(f"\n🏁 Testing completed at: {datetime.now()}")
    print("\n💡 Next steps:")
    print("1. Review the CSV output files for detailed trade analysis")
    print("2. Compare equity curves between baseline and optimized versions")
    print("3. Run parameter sweep with --enable-parameter-sweep for full optimization")
    print("4. Analyze threshold exceedance logs to fine-tune dynamic thresholds")

if __name__ == "__main__":
    main()
