#!/usr/bin/env python3
"""
Optimized trading simulation with enhanced trend reversal detection
Based on simulate_trading_new.py but with improved entry/exit logic
"""
import os, sys, json, logging, argparse
from pathlib import Path
from datetime import timedelta
import pandas as pd
import numpy as np
import torch
import gymnasium as gym

# Import our modules
from indicators import calculate_and_merge_indicators
from popart_sac import PopArtSAC
from agent import SimpleCNN1D, SafeReplayBuffer
from stable_baselines3.common.vec_env import VecNormalize, DummyVecEnv

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(name)s] [%(levelname)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
    handlers=[logging.StreamHandler(sys.stdout)]
)
log = logging.getLogger("OptimizedTrading")

def load_config(path: Path):
    """Load configuration from JSON file"""
    with open(path, 'r') as f:
        content = f.read()
        # Replace environment variables
        import re
        def replace_env_var(match):
            var_name = match.group(1)
            return os.environ.get(var_name, match.group(0))
        content = re.sub(r'\$\{([^}]+)\}', replace_env_var, content)
        cfg = json.loads(content)
    return cfg

class OptimizedTradingEngine:
    """Enhanced trading engine with trend reversal detection"""
    
    def __init__(self, cfg):
        self.cfg = cfg
        self.pos = 0  # Current position: 0=none, 1=long, -1=short
        self.size = 0.0
        self.entry_price = 0.0
        self.balance = cfg["account"]["initialEquity"]
        self.trades = []
        
        # Enhanced parameters for trend detection
        self.signal_history = []  # Track recent signals for trend analysis
        self.max_history = 10  # Keep last 10 signals
        
        # Dynamic thresholds based on market conditions
        self.base_entry_threshold = cfg.get("tradeParams", {}).get('entryActionThreshold', 0.7)
        self.base_exit_threshold = cfg.get("tradeParams", {}).get('exitActionThreshold', 0.7)
        
    def analyze_trend_strength(self, entry_sig):
        """Analyze trend strength based on signal history"""
        self.signal_history.append(entry_sig)
        if len(self.signal_history) > self.max_history:
            self.signal_history.pop(0)
            
        if len(self.signal_history) < 3:
            return 0.0, "insufficient_data"
            
        # Calculate trend metrics
        recent_signals = np.array(self.signal_history[-5:])  # Last 5 signals
        
        # Trend direction and strength
        trend_direction = np.sign(np.mean(recent_signals))
        trend_strength = np.abs(np.mean(recent_signals))
        
        # Momentum (acceleration)
        if len(recent_signals) >= 3:
            momentum = recent_signals[-1] - recent_signals[-3]
        else:
            momentum = 0.0
            
        # Volatility (signal consistency)
        volatility = np.std(recent_signals) if len(recent_signals) > 1 else 0.0
        
        return {
            'direction': trend_direction,
            'strength': trend_strength,
            'momentum': momentum,
            'volatility': volatility,
            'signals': recent_signals.tolist()
        }
    
    def get_dynamic_thresholds(self, trend_analysis, current_sig):
        """Calculate dynamic entry/exit thresholds based on market conditions"""
        
        # Base thresholds
        entry_threshold = self.base_entry_threshold
        exit_threshold = self.base_exit_threshold
        reversal_threshold = max(self.base_exit_threshold, 0.8)  # Higher threshold for reversals
        
        # Adjust based on trend strength
        if trend_analysis['strength'] > 0.5:
            # Strong trend - be more selective for entries, easier for reversals
            entry_threshold = min(entry_threshold + 0.1, 0.9)
            reversal_threshold = max(reversal_threshold - 0.1, 0.6)
        elif trend_analysis['strength'] < 0.3:
            # Weak trend - be more aggressive for entries
            entry_threshold = max(entry_threshold - 0.1, 0.5)
            
        # Adjust based on momentum
        if abs(trend_analysis['momentum']) > 0.3:
            # High momentum - easier reversals
            reversal_threshold = max(reversal_threshold - 0.1, 0.6)
            
        # Adjust based on volatility
        if trend_analysis['volatility'] > 0.4:
            # High volatility - require stronger signals
            entry_threshold = min(entry_threshold + 0.1, 0.9)
            reversal_threshold = min(reversal_threshold + 0.1, 0.9)
            
        return {
            'entry': entry_threshold,
            'exit': exit_threshold,
            'reversal': reversal_threshold
        }
    
    def execute_decision(self, action, current_price, current_time):
        """Execute trading decision with enhanced trend analysis"""
        entry_sig = action[0]
        exit_sig = action[3] if len(action) > 3 else 0.0
        
        # Analyze current trend
        trend_analysis = self.analyze_trend_strength(entry_sig)
        
        # Get dynamic thresholds
        thresholds = self.get_dynamic_thresholds(trend_analysis, entry_sig)
        
        # Log detailed analysis every 100 steps
        if len(self.signal_history) % 100 == 0:
            log.info(f"🔍 TREND ANALYSIS: sig={entry_sig:.3f}, strength={trend_analysis['strength']:.3f}, "
                    f"momentum={trend_analysis['momentum']:.3f}, volatility={trend_analysis['volatility']:.3f}")
            log.info(f"📊 THRESHOLDS: entry={thresholds['entry']:.3f}, reversal={thresholds['reversal']:.3f}")
        
        # Entry logic
        if self.pos == 0:
            triggered_pos = 0
            
            if entry_sig > thresholds['entry']:
                triggered_pos = 1
                log.info(f"🟢 LONG ENTRY: sig={entry_sig:.3f} > thr={thresholds['entry']:.3f}")
            elif entry_sig < -thresholds['entry']:
                triggered_pos = -1
                log.info(f"🔴 SHORT ENTRY: sig={entry_sig:.3f} < thr={-thresholds['entry']:.3f}")

            if triggered_pos != 0:
                self._open_position(triggered_pos, current_price, current_time, entry_sig)
        
        # Exit logic with trend reversal detection
        elif self.pos != 0:
            exit_triggered = False
            exit_reason = ""
            exit_price = current_price
            new_position = 0
            
            # Check for trend reversal (immediate position flip)
            if self.pos == 1 and entry_sig < -thresholds['reversal']:
                # Long position, strong short signal - REVERSAL
                exit_triggered = True
                exit_reason = "REVERSAL_TO_SHORT"
                new_position = -1
                log.info(f"🔄 TREND REVERSAL: LONG→SHORT, sig={entry_sig:.3f} < thr={-thresholds['reversal']:.3f}")
                
            elif self.pos == -1 and entry_sig > thresholds['reversal']:
                # Short position, strong long signal - REVERSAL
                exit_triggered = True
                exit_reason = "REVERSAL_TO_LONG"
                new_position = 1
                log.info(f"🔄 TREND REVERSAL: SHORT→LONG, sig={entry_sig:.3f} > thr={thresholds['reversal']:.3f}")
            
            # Standard SL/TP logic
            if not exit_triggered:
                if self.pos == 1:  # Long position
                    if current_price <= self.entry_price * 0.99:  # 1% SL
                        exit_triggered = True
                        exit_reason = "SL"
                        exit_price = self.entry_price * 0.99
                    elif current_price >= self.entry_price * 1.02:  # 2% TP
                        exit_triggered = True
                        exit_reason = "TP"
                        exit_price = self.entry_price * 1.02
                elif self.pos == -1:  # Short position
                    if current_price >= self.entry_price * 1.01:  # 1% SL
                        exit_triggered = True
                        exit_reason = "SL"
                        exit_price = self.entry_price * 1.01
                    elif current_price <= self.entry_price * 0.98:  # 2% TP
                        exit_triggered = True
                        exit_reason = "TP"
                        exit_price = self.entry_price * 0.98
            
            # Agent exit signal (standard exit without reversal)
            if not exit_triggered and abs(exit_sig) > thresholds['exit']:
                exit_triggered = True
                exit_reason = "AGENT_EXIT"
                exit_price = current_price
                log.info(f"🚪 AGENT EXIT: exit_sig={exit_sig:.3f} > thr={thresholds['exit']:.3f}")
            
            if exit_triggered:
                self._close_position(exit_price, current_time, exit_reason)
                
                # Open new position if reversal
                if new_position != 0:
                    self._open_position(new_position, current_price, current_time, entry_sig)
    
    def _open_position(self, direction, price, timestamp, signal):
        """Open a new position"""
        # Calculate position size
        risk_per_trade = self.cfg.get("tradeParams", {}).get("riskPerTrade", 0.01)
        risk_amount = self.balance * risk_per_trade
        
        # Simplified SL distance calculation
        min_sl_dist_perc = self.cfg.get("tradeParams", {}).get("minSLDistancePercent", 2.0) / 100.0
        sl_distance = price * min_sl_dist_perc
        
        self.size = risk_amount / sl_distance if sl_distance > 0 else 0
        self.pos = direction
        self.entry_price = price
        
        direction_str = "LONG" if direction == 1 else "SHORT"
        log.info(f"📈 {direction_str} ENTRY @ {price:.4f}, size: {self.size:.2f}, signal: {signal:.4f}")
    
    def _close_position(self, exit_price, timestamp, reason):
        """Close current position"""
        if self.pos == 0:
            return
            
        # Calculate PnL
        pnl = (exit_price - self.entry_price) * self.size * self.pos
        self.balance += pnl
        
        # Record trade
        trade = {
            'timestamp': timestamp,
            'entry_price': self.entry_price,
            'exit_price': exit_price,
            'size': self.size,
            'direction': self.pos,
            'pnl': pnl,
            'exit_reason': reason
        }
        self.trades.append(trade)
        
        direction_str = "LONG" if self.pos == 1 else "SHORT"
        log.info(f"📉 {direction_str} EXIT @ {exit_price:.4f}, PnL: ${pnl:.2f}, Reason: {reason}")
        
        # Reset position
        self.pos = 0
        self.size = 0.0
        self.entry_price = 0.0

def load_historical_data(symbol: str, start_date: str, end_date: str, timeframe: str = "1s"):
    """Load historical data from parquet files"""
    log.info(f"Loading historical data for {symbol} from {start_date} to {end_date}")
    
    # Convert dates
    start_dt = pd.to_datetime(start_date).tz_localize('UTC')
    end_dt = pd.to_datetime(end_date).tz_localize('UTC') + pd.Timedelta(days=1) - pd.Timedelta(seconds=1)
    
    # Load data from parquet files
    data_dir = Path("parquet_processed") / symbol / timeframe
    
    dfs = []
    current_date = start_dt.date()
    end_date_obj = end_dt.date()
    
    while current_date <= end_date_obj:
        file_path = data_dir / f"{current_date}.parquet"
        if file_path.exists():
            df = pd.read_parquet(file_path)
            df.index = pd.to_datetime(df.index, utc=True)
            dfs.append(df)
            log.info(f"Loaded {len(df)} rows from {file_path}")
        else:
            log.warning(f"File not found: {file_path}")
        
        current_date += timedelta(days=1)
    
    if not dfs:
        raise ValueError(f"No data files found for {symbol} in range {start_date} to {end_date}")
    
    # Combine all data
    data = pd.concat(dfs, axis=0)
    data = data.sort_index()
    
    # Filter to exact time range
    data = data[(data.index >= start_dt) & (data.index <= end_dt)]
    
    log.info(f"Total loaded: {len(data)} rows from {start_dt} to {end_dt}")
    return data

def main():
    """Main simulation function"""
    parser = argparse.ArgumentParser(description="Optimized Trading Simulation")
    parser.add_argument("--cfg", required=True, help="Configuration file path")
    parser.add_argument("--start", required=True, help="Start date (YYYY-MM-DD)")
    parser.add_argument("--end", required=True, help="End date (YYYY-MM-DD)")
    parser.add_argument("--out-trades", help="Output trades CSV file")
    parser.add_argument("--out-equity", help="Output equity CSV file")
    
    args = parser.parse_args()
    
    # Load configuration
    cfg = load_config(Path(args.cfg))
    symbol = cfg["symbol"]
    
    log.info(f"🚀 Starting optimized simulation for {symbol}")
    log.info(f"📅 Period: {args.start} to {args.end}")
    
    # Load historical data
    data = load_historical_data(symbol, args.start, args.end)
    
    # Calculate indicators
    log.info("📊 Calculating indicators...")
    data_with_indicators = calculate_and_merge_indicators(data, cfg)
    
    # Load model and VecNormalize
    model_path = cfg["model"]["path"]
    vecnorm_path = cfg["model"]["vecnormalize_path"]
    log.info(f"🤖 Loading model: {model_path}")
    log.info(f"📊 Loading VecNormalize: {vecnorm_path}")

    # Load VecNormalize
    try:
        vec_normalize = VecNormalize.load(vecnorm_path)
        vec_normalize.training = False
        log.info("✅ VecNormalize loaded successfully")
    except Exception as e:
        log.error(f"❌ Failed to load VecNormalize: {e}")
        return

    # Load model
    try:
        model = PopArtSAC.load(model_path)
        log.info("✅ Model loaded successfully")
    except Exception as e:
        log.error(f"❌ Failed to load model: {e}")
        return

    # Initialize trading engine
    te = OptimizedTradingEngine(cfg)

    # Prepare features
    feature_columns = cfg.get("features", [])
    log.info(f"📋 Using {len(feature_columns)} features")

    # Run simulation
    log.info("🎯 Starting optimized trading simulation...")

    equity_history = []
    lookback = 30  # Same as original simulation

    for i, (timestamp, row) in enumerate(data_with_indicators.iterrows()):
        if i < lookback:
            continue  # Skip initial rows for lookback

        current_price = row['close']

        # Prepare state for model
        try:
            # Get lookback window
            start_idx = max(0, i - lookback + 1)
            end_idx = i + 1
            window_data = data_with_indicators.iloc[start_idx:end_idx]

            # Extract features
            feature_data = []
            for col in feature_columns:
                if col in window_data.columns:
                    feature_data.append(window_data[col].values)
                else:
                    # Fill missing features with zeros
                    feature_data.append(np.zeros(len(window_data)))

            # Stack features
            state = np.column_stack(feature_data)
            state = state.flatten().astype(np.float32)

            # Reshape for model input
            state_input = state.reshape(1, -1)

            # Apply VecNormalize
            try:
                state_normalized = vec_normalize.normalize_obs(state_input)
            except Exception as e:
                log.warning(f"VecNormalize error: {e}, using raw state")
                state_normalized = state_input

            # Get model prediction
            action, _ = model.predict(state_normalized[0], deterministic=True)

        except Exception as e:
            log.warning(f"Model prediction error at step {i}: {e}")
            # Fallback to neutral action
            action = [0.0, 0.0, 0.0, 0.0]
        
        # Execute trading decision
        te.execute_decision(action, current_price, timestamp)
        
        # Track equity
        unrealized_pnl = 0.0
        if te.pos != 0 and te.entry_price > 0:
            unrealized_pnl = (current_price - te.entry_price) * te.size * te.pos
        
        total_equity = te.balance + unrealized_pnl
        equity_history.append({
            'timestamp': timestamp,
            'equity': total_equity,
            'realized_pnl': te.balance - cfg["account"]["initialEquity"],
            'unrealized_pnl': unrealized_pnl,
            'position': te.pos
        })
        
        # Progress logging
        if i % 1000 == 0:
            log.info(f"📈 Step {i}/{len(data_with_indicators)}: Equity=${total_equity:.2f}, Pos={te.pos}")
    
    # Final results
    final_equity = te.balance
    total_pnl = final_equity - cfg["account"]["initialEquity"]
    
    log.info(f"🏁 SIMULATION COMPLETE")
    log.info(f"💰 Final Equity: ${final_equity:.2f}")
    log.info(f"📊 Total PnL: ${total_pnl:.2f} ({total_pnl/cfg['account']['initialEquity']*100:.2f}%)")
    log.info(f"🔢 Total Trades: {len(te.trades)}")
    
    # Save results
    if args.out_trades and te.trades:
        trades_df = pd.DataFrame(te.trades)
        trades_df.to_csv(args.out_trades, index=False)
        log.info(f"💾 Trades saved to: {args.out_trades}")
    
    if args.out_equity:
        equity_df = pd.DataFrame(equity_history)
        equity_df.to_csv(args.out_equity, index=False)
        log.info(f"💾 Equity history saved to: {args.out_equity}")

if __name__ == "__main__":
    main()
