#!/usr/bin/env python3
"""
Test script pre overenie integr<PERSON><PERSON> rolling indik<PERSON><PERSON>ov do precompute_features.py
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os
import tempfile
import shutil

# Pridanie cesty k modulu
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from precompute_features import precompute_features_for_simulation

def create_test_raw_data(output_dir, num_hours=2):
    """Vytvorí testové surové dáta pre test"""
    
    # Vytvorenie adresárovej štruktúry
    ohlcv_dir = os.path.join(output_dir, "ohlcv", "1s")
    os.makedirs(ohlcv_dir, exist_ok=True)
    
    # Vytvorenie testových OHLCV dát pre jeden deň
    start_time = datetime(2024, 1, 1, 10, 0, 0)
    num_rows = num_hours * 3600  # 1s dáta
    
    timestamps = [start_time + timedelta(seconds=i) for i in range(num_rows)]
    
    # Simu<PERSON><PERSON><PERSON> cenového pohybu
    np.random.seed(42)
    base_price = 0.5  # XRP cena
    price_changes = np.random.normal(0, 0.001, num_rows)
    
    close_prices = [base_price]
    for change in price_changes[1:]:
        new_price = close_prices[-1] * (1 + change)
        close_prices.append(new_price)
    
    # Vytvorenie OHLC dát
    data = []
    for i, (ts, close) in enumerate(zip(timestamps, close_prices)):
        volatility = abs(np.random.normal(0, 0.0005))
        high = close * (1 + volatility)
        low = close * (1 - volatility)
        open_price = close_prices[i-1] if i > 0 else close
        
        data.append({
            'timestamp': ts,
            'open': max(open_price, low),
            'high': max(open_price, high, close),
            'low': min(open_price, low, close),
            'close': close,
            'volume': np.random.uniform(1000, 10000),
            'count': np.random.randint(10, 100)
        })
    
    df = pd.DataFrame(data)
    
    # Uloženie do parquet súboru
    output_file = os.path.join(ohlcv_dir, "2024-01-01.parquet")
    df.to_parquet(output_file, engine='pyarrow', index=False)
    
    print(f"✅ Vytvorené testové OHLCV dáta: {output_file}")
    print(f"   Riadkov: {len(df)}, Časové rozpätie: {df['timestamp'].min()} - {df['timestamp'].max()}")
    
    return output_file

def test_precompute_integration():
    """Test integrácie rolling indikátorov"""
    
    print("🧪 Testovanie integrácie rolling indikátorov do precompute_features...")
    
    # Vytvorenie dočasných adresárov
    temp_raw_dir = tempfile.mkdtemp(prefix="test_raw_")
    temp_output_dir = tempfile.mkdtemp(prefix="test_output_")
    
    try:
        # Vytvorenie testových dát
        print("📊 Vytváram testové surové dáta...")
        create_test_raw_data(temp_raw_dir, num_hours=2)
        
        # Spustenie precompute_features
        print("🔧 Spúšťam precompute_features s rolling indikátormi...")
        
        config_path = "test_config_1s_rolling.json"
        start_date = "2024-01-01"
        end_date = "2024-01-01"
        
        try:
            precompute_features_for_simulation(
                config_path=config_path,
                raw_data_dir=temp_raw_dir,
                output_dir=temp_output_dir,
                start_date=start_date,
                end_date=end_date
            )
            
            print("✅ precompute_features dokončené bez chyby!")
            
            # Kontrola výsledkov
            print("🔍 Kontrolujem výsledky...")
            
            output_symbol_dir = os.path.join(temp_output_dir, "XRPUSDC", "1s")
            if os.path.exists(output_symbol_dir):
                output_files = os.listdir(output_symbol_dir)
                print(f"   Vytvorené súbory: {output_files}")
                
                if output_files:
                    # Načítanie prvého súboru
                    first_file = os.path.join(output_symbol_dir, output_files[0])
                    df = pd.read_parquet(first_file)
                    
                    print(f"   📊 Načítaný súbor: {first_file}")
                    print(f"   📈 Shape: {df.shape}")
                    print(f"   📋 Stĺpce: {list(df.columns)}")
                    
                    # Kontrola rolling features
                    rolling_features = ['supertrend_1h_rolling', 'adx_1h_rolling']
                    for feature in rolling_features:
                        if feature in df.columns:
                            valid_count = df[feature].dropna().count()
                            total_count = len(df)
                            print(f"   ✅ {feature}: {valid_count}/{total_count} platných hodnôt")
                            
                            if valid_count > 0:
                                min_val = df[feature].min()
                                max_val = df[feature].max()
                                last_val = df[feature].dropna().iloc[-1] if valid_count > 0 else np.nan
                                print(f"      Rozsah: {min_val:.6f} - {max_val:.6f}, Posledná: {last_val:.6f}")
                        else:
                            print(f"   ❌ {feature}: CHÝBA!")
                    
                    # Ukážka posledných riadkov
                    print("   📋 Posledných 3 riadky:")
                    display_cols = ['timestamp', 'close', 'supertrend_1h_rolling', 'adx_1h_rolling']
                    available_cols = [col for col in display_cols if col in df.columns]
                    print(df[available_cols].tail(3).to_string(index=False))
                    
                else:
                    print("   ❌ Žiadne výstupné súbory!")
            else:
                print(f"   ❌ Výstupný adresár neexistuje: {output_symbol_dir}")
                
        except Exception as e:
            print(f"❌ Chyba pri precompute_features: {e}")
            import traceback
            traceback.print_exc()
            
    finally:
        # Vyčistenie dočasných súborov
        print("🧹 Čistím dočasné súbory...")
        shutil.rmtree(temp_raw_dir, ignore_errors=True)
        shutil.rmtree(temp_output_dir, ignore_errors=True)
        
    print("✅ Test dokončený!")

if __name__ == "__main__":
    test_precompute_integration()
