import math
import os
import argparse
import logging
from pathlib import Path
from datetime import datetime, timedelta, timezone
import yaml
import json
import pandas as pd
import numpy as np
import pyarrow.parquet as pq
from stable_baselines3 import SAC  # still allow fallback, but not used for PopArt checkpoints
from stable_baselines3.common.vec_env import VecNormalize
from popart_sac import PopArtSAC    # <--- CUSTOM class to load PopArt models

# Import the prepare_df function from agent.py
def _dt(s: str) -> datetime:
    return datetime.fromisoformat(s.replace("Z", "+00:00")).astimezone(timezone.utc)

def prepare_df_for_simulation(cfg, start, end):
    """Same logic as in agent.py"""
    from joblib import Parallel, delayed
    
    symbol = cfg["symbol"]
    tf = cfg["primaryTimeframe"]
    
    root = Path(os.environ.get("PARQUET_DIR", cfg.get("featureParquetDir", "parquet"))).expanduser()
    tf_dir = next((d for d in (root/symbol/tf, root/symbol/"ohlcv"/tf) if d.exists()), None)
    if tf_dir is None:
        raise FileNotFoundError(f"No parquet dir under {root}")
    
    warm = start - timedelta(hours=24)
    
    def load_single_day(fp):
        if fp.exists():
            df = pd.read_parquet(fp, engine="pyarrow")
            if "timestamp" in df.columns:
                df.set_index("timestamp", inplace=True)
            df.index = pd.to_datetime(df.index, utc=True)
            return df
        return None
    
    file_paths = []
    cur = warm.date()
    while cur <= end.date():
        file_paths.append(tf_dir / f"{cur}.parquet")
        cur += timedelta(days=1)
    
    dfs = Parallel(n_jobs=8)(delayed(load_single_day)(fp) for fp in file_paths)
    
    dfs_filtered = [df for df in dfs if df is not None]
    if not dfs_filtered:
        raise RuntimeError("no parquet files in range")
    
    df_all = pd.concat(dfs_filtered).sort_index().loc[warm:end]
    feats = cfg["envSettings"]["feature_columns"]
    
    missing = [c for c in feats if c not in df_all.columns]
    if missing:
        log.warning("Missing %d columns → filled with zeros: %s", len(missing), ", ".join(missing))
        df_all = df_all.copy()
        for col in missing:
            df_all[col] = 0.0
    
    df_all = df_all.copy()
    df_all[feats] = df_all[feats].ffill()
    
    vol_cols = [c for c in feats if "vol" in c]
    df_all[vol_cols] = np.log1p(df_all[vol_cols])
    
    df_all.replace([np.inf, -np.inf], np.nan, inplace=True)
    df_all = df_all[np.isfinite(df_all[feats]).all(axis=1)]
    return df_all.loc[start:end, feats].astype("float32")

custom_objects = {
    # 1️⃣  buffer – prebijeme triedu a vypneme “timeout termination”,
    "replay_buffer_kwargs": dict(handle_timeout_termination=False),

    # 2️⃣  SB3 loaderu povieme, že optimalizácia pamäte netreba
    "optimize_memory_usage": False,

    # 3️⃣  picklené λ-funkcie zahoď a nahraď konštantou/None
    "learning_rate": 0.0,        # scalar namiesto lambda
    "lr_schedule":   None,
}

# Pokus o import gymnasium alebo gym pre prístup k spaces.Box
try:
    import gymnasium as gym_spaces
    log_gym_lib = "gymnasium"
except ImportError:
    try:
        import gym as gym_spaces
        log_gym_lib = "gym"
    except ImportError:
        print("WARNING: Ani 'gymnasium' ani 'gym' knižnica nenájdená. Budú použité predvolené hranice [-5, 5].")
        gym_spaces = None
        log_gym_lib = "None"

# --- Konfigurácia loggera ---
log = logging.getLogger("backtester")

# --- Pomocné funkcie ---
def load_config(config_path: Path) -> dict:
    """Načíta konfiguračný súbor YAML/JSON."""
    log.info(f"Načítavam konfiguráciu z: {config_path}")
    try:
        with open(config_path, 'r') as f:
            if config_path.suffix.lower() == '.json': config = json.load(f)
            elif config_path.suffix.lower() in ['.yaml', '.yml']: config = yaml.safe_load(f)
            else: raise ValueError(f"Nepodporovaný formát config súboru: {config_path.suffix}")
    except Exception as e: log.error(f"Chyba pri načítaní/spracovaní configu {config_path}: {e}"); raise

    try:
        # Pridanie .zip k ceste modelu, ak tam ešte nie je a cesta existuje
        model_path_str = config['trainingSettings']['modelSavePath']
        if not model_path_str.endswith(".zip"): model_path_str += ".zip"

        config['runtime'] = {
            'data_dir': Path(config['featureParquetDir']),
            'model_path': Path(model_path_str)
        }
    except KeyError as e: raise ValueError(f"Chýbajúci kľúč v konfigurácii potrebný pre runtime cesty: {e}")

    log.info(f"Konfigurácia načítaná pre symbol: {config.get('symbol', 'N/A')}")
    return config

def load_backtest_data(config: dict, start_date: datetime, end_date: datetime, load_second_data: bool = True, use_1s_decisions: bool = False) -> dict:
    """
    Načíta a spojí denné Parquet súbory pre backtest obdobie.
    Načíta aj skoršie dáta na "zahriatie" indikátorov s dlhým lookbackom.
    Ak load_second_data=True, načíta aj 1-sekundové dáta pre presnejšie simulácie SL/TP.
    Ak use_1s_decisions=True, pripraví dáta pre 1s rozhodovanie s 5m features.
    Vracia slovník s 'primary' (5m/1s) a voliteľne 'second' (1s) dataframe.
    """
    symbol = config.get('symbol')
    tf_cfg = config.get('primaryTimeframe')
    # Ak je tf zoznam → použi prvý (hlavný) timeframe
    timeframe = tf_cfg[0] if isinstance(tf_cfg, (list, tuple)) else tf_cfg
    data_dir = config.get('runtime', {}).get('data_dir')
    if not all([symbol, timeframe, data_dir]):
        raise ValueError("Chýbajúce kľúče 'symbol'/'primaryTimeframe'/'runtime.data_dir'.")

    # --- Výpočet maximálneho lookbacku pre zahriatie ---
    max_lookback_periods = 0
    # Základný lookback pre stav agenta
    state_lookback = config.get('envSettings', {}).get('state_lookback', 0)
    max_lookback_periods = max(max_lookback_periods, state_lookback)

    # Lookbacky z indikátorov (pridajte ďalšie podľa potreby)
    indicator_settings = config.get('indicatorSettings', {})
    if indicator_settings.get('hmm_5m', {}).get('enabled'):
        max_lookback_periods = max(max_lookback_periods, indicator_settings['hmm_5m'].get('window', 0))
    if indicator_settings.get('bollinger_5m', {}).get('enabled'):
        max_lookback_periods = max(max_lookback_periods, indicator_settings['bollinger_5m'].get('basePeriod', 0))
    if indicator_settings.get('atr_5m', {}).get('enabled'):
        max_lookback_periods = max(max_lookback_periods, indicator_settings['atr_5m'].get('basePeriod', 0))
    if indicator_settings.get('rsi_5m', {}).get('enabled'):
        max_lookback_periods = max(max_lookback_periods, indicator_settings['rsi_5m'].get('basePeriod', 0))
    if indicator_settings.get('adx', {}).get('enabled'):
        # ADX často potrebuje viac ako basePeriod kvôli vyhladzovaniu
        max_lookback_periods = max(max_lookback_periods, indicator_settings['adx'].get('basePeriod', 0) * 2)
    if indicator_settings.get('ema_5m', {}).get('enabled'):
        max_lookback_periods = max(max_lookback_periods, indicator_settings['ema_5m'].get('baseSlowPeriod', 0))
        max_lookback_periods = max(max_lookback_periods, indicator_settings['ema_5m'].get('baseFastPeriod', 0))
    # ... pridajte ďalšie kontroly pre vaše indikátory ...

    # Pridajme malý buffer pre istotu
    required_lookback_periods = max_lookback_periods + 10 # Napr. +10 periód buffer
    log.info(f"Maximálny potrebný lookback pre indikátory (vrátane buffera): {required_lookback_periods} periód")

    # --- Výpočet počtu dní na načítanie navyše ---
    try:
        tf_value = int(timeframe[:-1]) # Získa číslo z timeframe stringu, napr. 5 z '5m'
        tf_unit = timeframe[-1].lower() # Získa jednotku, napr. 'm' z '5m'
        if tf_unit == 'm': periods_per_day = 24 * 60 / tf_value
        elif tf_unit == 'h': periods_per_day = 24 / tf_value
        elif tf_unit == 'd': periods_per_day = 1
        else: raise ValueError(f"Nepodporovaná jednotka timeframe: {tf_unit}")

        # Odhad dní potrebných pre lookback + buffer (zaokrúhlené nahor)
        days_to_load_extra = math.ceil(required_lookback_periods / periods_per_day) + 2 # +2 dni buffer pre istotu/víkendy
    except Exception as e:
        log.warning(f"Nepodarilo sa presne určiť počet dní pre lookback z timeframe '{timeframe}': {e}. Používam fixný počet 5 dní navyše.")
        days_to_load_extra = 5 # Bezpečná predvolená hodnota

    load_start_date = start_date - timedelta(days=days_to_load_extra)
    log.info(f"Rozšírené obdobie načítavania dát pre zahriatie indikátorov: {load_start_date.date()} -> {end_date.date()}")

    # --- Načítanie primárnych dát (5m) z rozšíreného obdobia ---
    symbol_tf_dir = data_dir / symbol / timeframe
    all_files = []
    current_date = load_start_date
    while current_date <= end_date:
        day_file = symbol_tf_dir / f"{current_date.date()}.parquet"
        if day_file.exists():
            all_files.append(day_file)
        else:
            # Logujeme varovanie iba ak ide o dni v rámci *pôvodného* backtest rozsahu
            if current_date >= start_date:
                log.warning(f"Súbor pre {current_date.date()} neexistuje: {day_file}")
        current_date += timedelta(days=1)

    if not all_files:
        raise FileNotFoundError(f"Žiadne Parquet súbory pre symbol/timeframe {symbol}/{timeframe} v adresári {symbol_tf_dir} pre rozšírené obdobie.")
    log.info(f"Našlo sa {len(all_files)} súborov v rozšírenom období.")

    primary_dfs = []
    for f in all_files:
        try:
            primary_dfs.append(pd.read_parquet(f))
        except Exception as e:
            log.error(f"Chyba pri načítavaní {f}: {e}")
            raise
    if not primary_dfs:
        raise ValueError("Nepodarilo sa načítať žiadne dáta z Parquet súborov.")

    primary_df = pd.concat(primary_dfs, ignore_index=True)
    log.info(f"Spojených {len(primary_df)} riadkov z rozšíreného obdobia (5m).")

    # --- Načítanie sekundárnych dát (1s) ak je požadované alebo pre 1s decisions ---
    second_df = None
    if load_second_data or use_1s_decisions:
        second_timeframe = "1s"
        symbol_second_dir = data_dir / symbol / second_timeframe
        if not symbol_second_dir.exists():
            if use_1s_decisions:
                raise FileNotFoundError(f"1s dáta sú potrebné pre 1s rozhodovanie, ale adresár neexistuje: {symbol_second_dir}")
            log.warning(f"Adresár pre 1s dáta neexistuje: {symbol_second_dir}. Použijeme len 5m dáta.")
        else:
            second_files = []
            # Pre 1s decisions potrebujeme zahriatie aj v 1s dátach
            load_start_for_1s = load_start_date if use_1s_decisions else start_date
            current_date = load_start_for_1s
            while current_date <= end_date:
                day_file = symbol_second_dir / f"{current_date.date()}.parquet"
                if day_file.exists():
                    second_files.append(day_file)
                current_date += timedelta(days=1)

            if second_files:
                log.info(f"Našlo sa {len(second_files)} súborov 1s dát.")
                second_dfs = []
                for f in second_files:
                    try:
                        second_dfs.append(pd.read_parquet(f))
                    except Exception as e:
                        if use_1s_decisions:
                            raise RuntimeError(f"Chyba pri načítavaní kritických 1s dát {f}: {e}")
                        log.warning(f"Chyba pri načítavaní 1s dát {f}: {e}. Preskakujem.")

                if second_dfs:
                    second_df = pd.concat(second_dfs, ignore_index=True)
                    log.info(f"Spojených {len(second_df)} riadkov 1s dát.")

                    # Spracovanie timestamp pre 1s dáta
                    if 'timestamp' not in second_df.columns:
                        if use_1s_decisions:
                            raise ValueError("Chýba stĺpec 'timestamp' v 1s dátach potrebných pre 1s rozhodovanie.")
                        log.warning("Chýba stĺpec 'timestamp' v 1s dátach. Použijeme len 5m dáta.")
                        second_df = None
                    else:
                        # Konverzia na datetime a nastavenie ako index
                        if not isinstance(second_df.index, pd.DatetimeIndex):
                            second_df['timestamp'] = pd.to_datetime(second_df['timestamp'], utc=True)
                            second_df = second_df.sort_values('timestamp').set_index('timestamp')
                        else:
                            # Zabezpečíme, že index je UTC a zoradený
                            if second_df.index.tz is None:
                                second_df.index = second_df.index.tz_localize("UTC")
                            elif second_df.index.tz != timezone.utc:
                                second_df.index = second_df.index.tz_convert("UTC")
                            second_df = second_df.sort_index()

                        # Kontrola, či máme potrebné stĺpce pre SL/TP v 1s dátach
                        required_cols = ['open', 'high', 'low', 'close']
                        missing_cols = [col for col in required_cols if col not in second_df.columns]
                        if missing_cols:
                            if use_1s_decisions:
                                raise ValueError(f"V 1s dátach chýbajú potrebné stĺpce pre 1s rozhodovanie: {missing_cols}")
                            log.warning(f"V 1s dátach chýbajú potrebné stĺpce: {missing_cols}. Použijeme len 5m dáta.")
                            second_df = None
                elif use_1s_decisions:
                    raise RuntimeError("Nepodarilo sa načítať žiadne 1s dáta potrebné pre 1s rozhodovanie.")

    # --- Spracovanie Timestamp a zoradenie ---
    if 'timestamp' not in primary_df.columns:
        raise ValueError("Chýba stĺpec 'timestamp' v načítaných dátach")
    # Konverzia na datetime a nastavenie ako index (ak ešte nie je)
    if not isinstance(primary_df.index, pd.DatetimeIndex):
         primary_df['timestamp'] = pd.to_datetime(primary_df['timestamp'], utc=True)
         primary_df = primary_df.sort_values('timestamp').set_index('timestamp')
    else:
        # Zabezpečíme, že index je UTC a zoradený
        if primary_df.index.tz is None:
            primary_df.index = primary_df.index.tz_localize("UTC")
        elif primary_df.index.tz != timezone.utc:
            primary_df.index = primary_df.index.tz_convert("UTC")
        primary_df = primary_df.sort_index()

    # --- Kontrola Features a definovanie Essential Columns ---
    try:
        feature_cols = config['envSettings']['feature_columns']
        assert isinstance(feature_cols, list)
    except (KeyError, AssertionError):
        raise ValueError("Chýbajúci alebo neplatný 'envSettings.feature_columns' v konfigurácii.")
    log.info(f"Očakávaných {len(feature_cols)} features podľa configu.")

    missing_cols = [col for col in feature_cols if col not in primary_df.columns]
    if missing_cols:
        log.warning(f"Nasledujúce feature stĺpce z configu chýbajú v dátach a budú nahradené nulami: {missing_cols}")
        for col in missing_cols:
            primary_df[col] = 0.0 # Alebo iná vhodná hodnota, napr. np.nan, ak by dropna malo zafungovať

    # Overenie existencie stĺpca pre dynamickú veľkosť pozície
    if "depth_imbalance5" in primary_df.columns:
        config['runtime']['has_imbalance'] = True
    else:
        log.warning("Stĺpec 'depth_imbalance5' chýba v dátach, dynamický size boost nebude použitý.")
        config['runtime']['has_imbalance'] = False


    present_feature_cols = [col for col in feature_cols if col in primary_df.columns]
    other_cols = [col for col in primary_df.columns if col not in present_feature_cols]
    primary_df = primary_df[present_feature_cols + other_cols] # Udržuje poradie features na začiatku
    log.info(f"Počet features prítomných v DataFrame: {len(present_feature_cols)}")

    # Definícia esenciálnych stĺpcov pre dropna
    essential_cols = present_feature_cols + ['open', 'high', 'low', 'close']

    # Nájdenie a pridanie ATR stĺpca
    atr_col_name = config.get('runtime', {}).get('atr_column')
    if not atr_col_name:
        # Skús nájsť stĺpec začínajúci na ATR_, ak nie je definovaný
        config['runtime']['atr_column'] = next((c for c in primary_df.columns if c.startswith('ATR_')), None)
        atr_col_name = config['runtime']['atr_column']

    risk_perc_sizing = config.get('riskManagement', {}).get('positionSizingMethod') == 'RiskPercentage'
    if atr_col_name:
        if atr_col_name in primary_df.columns:
            essential_cols.append(atr_col_name)
            log.info(f"Identifikovaný ATR stĺpec pre SL/veľkosť: {atr_col_name}")
        else:
             log.warning(f"ATR stĺpec '{atr_col_name}' definovaný v configu alebo runtime nie je v dátach!")
             # Ak je potrebný pre sizing, vyhodíme chybu
             if risk_perc_sizing:
                 raise ValueError(f"Chýba ATR stĺpec '{atr_col_name}' potrebný pre RiskPercentage position sizing.")
    elif risk_perc_sizing:
        raise ValueError("Nebol nájdený žiadny ATR stĺpec (napr. 'ATR_14') v dátach, ktorý je potrebný pre RiskPercentage position sizing.")

    # Pridáme aj imbalance stĺpec k esenciálnym, ak existuje a používa sa
    if config['runtime']['has_imbalance']:
        if "depth_imbalance5" in primary_df.columns:
             essential_cols.append("depth_imbalance5")

    # Odstránime duplikáty a zabezpečíme, že pracujeme len s existujúcimi stĺpcami
    essential_cols = list(set(e for e in essential_cols if e in primary_df.columns))
    log.info(f"Počet esenciálnych stĺpcov pre kontrolu NaN: {len(essential_cols)}")
    log.debug(f"Esenciálne stĺpce: {essential_cols}")


    # --- Odstránenie NaN riadkov z ROZŠÍRENÉHO datasetu ---
    initial_rows_extended = len(primary_df)
    primary_df.dropna(subset=essential_cols, inplace=True)
    rows_dropped_extended = initial_rows_extended - len(primary_df)
    if rows_dropped_extended > 0:
        log.info(f"Odstránených {rows_dropped_extended} riadkov kvôli NaN z rozšíreného datasetu (zahrievacie obdobie).")
    if primary_df.empty:
        raise ValueError("Žiadne dáta nezostali po odstránení NaN z rozšíreného datasetu.")

    # --- Orezanie DataFrame na PÔVODNÝ požadovaný časový rámec ---
    log.info(f"Orezávam dáta na pôvodný požadovaný rozsah: {start_date} -> {end_date}")
    primary_df_original_range = primary_df.loc[start_date:end_date].copy() # Použijeme .copy(), aby sme sa vyhli SettingWithCopyWarning

    if primary_df_original_range.empty:
        raise ValueError("Po orezaní na pôvodný časový rozsah nezostali žiadne dáta. Skontrolujte dostupnosť dát a funkčnosť dropna.")

    final_rows = len(primary_df_original_range)
    log.info(f"Finálny počet riadkov pre backtest po zahriatí a orezaní: {final_rows}")

    # Záverečné logovanie stĺpcov
    cols_to_log = primary_df_original_range.columns[:5].tolist() + ['...'] + primary_df_original_range.columns[-5:].tolist()
    log.info(f"Dostupné stĺpce vo finálnom DataFrame ({len(primary_df_original_range.columns)}): {', '.join(cols_to_log)}")

    # Kontrola, či máme dosť dát aspoň pre prvý krok agenta
    if final_rows < state_lookback:
         log.warning(f"Finálny počet riadkov ({final_rows}) je menší ako potrebný lookback agenta ({state_lookback}). Agent nebude môcť urobiť predikciu.")
         # Môžeme tu buď skončiť s chybou, alebo nechať backtest bežať (ale neurobí žiadny krok)
         # raise ValueError("Nedostatok dát pre prvý krok agenta po zahriatí a orezaní.")

    # --- Príprava dát pre 1s rozhodovanie (BEZ LOOK-AHEAD BIAS) ---
    if use_1s_decisions and second_df is not None:
        log.info("Pripravujem dáta pre 1s rozhodovanie - bez look-ahead bias...")
        
        # Debug: Log the date ranges and data availability
        log.info(f"1s data index range: {second_df.index.min()} -> {second_df.index.max()}")
        log.info(f"Requested range: {start_date} -> {end_date}")
        log.info(f"1s data columns: {list(second_df.columns)}")
        
        # Check if required columns exist in 1s data
        required_cols = ['open', 'high', 'low', 'close']
        missing_cols = [col for col in required_cols if col not in second_df.columns]
        if missing_cols:
            raise ValueError(f"Missing required columns in 1s data: {missing_cols}")
        
        # Orezanie 1s dát na pôvodný rozsah - iba OHLCV pre real-time forward-fill
        second_df_original_range = second_df.loc[start_date:end_date][['open', 'high', 'low', 'close']].copy()
        
        log.info(f"Pripravených {len(second_df_original_range)} 1s OHLCV riadkov pre real-time forward-fill.")
        
        if len(second_df_original_range) == 0:
            log.error(f"No 1s data found in range {start_date} -> {end_date}")
            log.error(f"Available 1s data range: {second_df.index.min()} -> {second_df.index.max()}")
            raise ValueError(f"No 1s data available for the requested backtest period. Check if 1s data exists for {start_date.date()} to {end_date.date()}")
        
        log.info("Forward-fill 5m features sa bude robiť REAL-TIME počas simulácie!")
        
        # Vrátime 1s OHLCV dáta ako second, 5m ako primary pre real-time forward-fill
        return {
            'primary': primary_df_original_range,      # 5m dáta s features
            'second': second_df_original_range,        # 1s OHLCV pre real-time decisions
            'use_1s_decisions': True                   # Flag pre real-time forward-fill
        }

    # Vrátime slovník s oboma dataframe-ami (pôvodné správanie)
    return {
        'primary': primary_df_original_range,
        'second': second_df
    }

# <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
# >>>>>>>>>>>>>>>>>>>>>>>>>>>>> UPRAVENÁ FUNKCIA <<<<<<<<<<<<<<<<<<<<<<<<<<<<<
# <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
def get_state(df_features: pd.DataFrame, current_step_index: int, lookback: int,
              current_pos: int, entry_price: float, current_price: float,
              current_sl_price: float, current_tp_price: float,
              open_idx_sim: int, current_atr: float,
              inactivity_limit: int, tsl_enabled: bool,
              use_1s_decisions: bool = False, features_5m: pd.DataFrame = None,
              main_timestamps: pd.Index = None, current_time: pd.Timestamp = None) -> np.ndarray:
    """
    Pripraví stavový vektor podľa logiky ScalpingEnv._state.
    Vracia sploštený vektor tvaru (observation_space.shape[0],).
    
    Ak use_1s_decisions=True, vytvára real-time forward-fill features pre lookback window.
    """
    if current_step_index < lookback - 1:
        raise IndexError(f"Nedostatok histórie ({current_step_index+1}) pre lookback={lookback}.")

    if use_1s_decisions:
        # Real-time forward-fill mode - vytvoríme features pre lookback window
        if features_5m is None or main_timestamps is None or current_time is None:
            raise ValueError("V 1s mode sú potrebné features_5m, main_timestamps a current_time")
        
        # Získame lookback window timestamps
        start_idx = current_step_index - lookback + 1
        end_idx = current_step_index + 1
        lookback_timestamps = main_timestamps[start_idx:end_idx]
        
        if len(lookback_timestamps) != lookback:
            raise ValueError(f"Získaných {len(lookback_timestamps)} timestamps namiesto {lookback}")
        
        # Pre každý timestamp v lookback window vytvoríme real-time forward-fill features
        frame_data = []
        for ts in lookback_timestamps:
            # Nájdi posledné dostupné 5m features pred/na ts (BEZ LOOK-AHEAD)
            mask_5m = features_5m.index <= ts
            available_5m = features_5m[mask_5m]
            
            if not available_5m.empty:
                # Vezmi posledný dostupný 5m bar a jeho features (forward-fill)
                latest_5m_features = available_5m.iloc[-1].values
                frame_data.append(latest_5m_features)
            else:
                # Ak ešte žiadne 5m features nie sú dostupné, naplň nulami
                frame_data.append(np.zeros(len(features_5m.columns)))
        
        # Konverzia na numpy array
        frame_array = np.array(frame_data, dtype=np.float32)
        
        if frame_array.shape[0] != lookback:
            raise ValueError(f"Real-time forward-fill vytvoril {frame_array.shape[0]} riadkov namiesto {lookback}")
            
    else:
        # Pôvodný 5m mode
        start_idx = current_step_index - lookback + 1
        end_idx = current_step_index + 1
        frame_data = df_features.iloc[start_idx:end_idx]
        
        if len(frame_data) != lookback:
            raise ValueError(f"Získaných {len(frame_data)} riadkov namiesto {lookback} pre index {current_step_index}")

        # Ensure we have a contiguous numpy array with the right dtype
        frame_array = np.ascontiguousarray(frame_data.values).astype(np.float32)

    # Orezanie dát z trhu a sploštenie
    frame_flat_clipped = np.clip(frame_array, -5.0, 5.0).flatten()

    # --- Výpočet dodatočných stavových hodnôt (podľa ScalpingEnv) ---
    _MIN_PRICE = 1e-6
    _PNL_NORM_DENOM = 0.01 # Predpokladáme rovnakú hodnotu ako v env

    safe_current_price = max(current_price, _MIN_PRICE)
    safe_entry_price = max(entry_price, _MIN_PRICE)

    # 1. PnL Normalizované
    pnl_norm = 0.0
    if current_pos != 0 and entry_price > 0:
        pnl_norm = np.tanh(((safe_current_price - safe_entry_price) * current_pos) /
                           (safe_entry_price * _PNL_NORM_DENOM))

    # 2. Trvanie obchodu Normalizované
    trade_duration = 0.0
    if current_pos != 0 and open_idx_sim >= 0 and inactivity_limit > 0:
        trade_duration = np.tanh((current_step_index - open_idx_sim) / (inactivity_limit * 0.5))

    # 3. Status profitu (+1 profit, -1 strata, 0 flat/nula)
    profit_status = 0.0
    if current_pos != 0 and entry_price > 0:
        profit_status = np.sign((safe_current_price - safe_entry_price) * current_pos)

    # 4. & 5. Vzdialenosť od TP a SL Normalizovaná
    tp_distance = 0.0
    sl_distance = 0.0
    if current_pos != 0 and np.isfinite(current_tp_price) and np.isfinite(current_sl_price) and current_tp_price > 0 and current_sl_price > 0:
        tp_norm_factor = safe_current_price * 0.01 # Normalizácia 1%
        sl_norm_factor = safe_current_price * 0.01
        if abs(tp_norm_factor) > 1e-9:
            tp_distance = np.tanh(abs(current_tp_price - safe_current_price) / tp_norm_factor) * np.sign(current_tp_price - safe_current_price) * current_pos
        if abs(sl_norm_factor) > 1e-9:
            sl_distance = np.tanh(abs(current_sl_price - safe_current_price) / sl_norm_factor) * np.sign(current_sl_price - safe_current_price) * current_pos

    # 6. PnL / ATR Ratio Normalizované
    pnl_atr_ratio = 0.0
    # Použijeme current_atr priamo, predpokladáme, že je už validované
    if current_pos != 0 and tsl_enabled and current_atr > 1e-9 and entry_price > 0:
        pnl_atr_ratio = np.tanh(((safe_current_price - safe_entry_price) * current_pos) / current_atr)

    # --- TIME FEATURES podľa ENV ---
    if use_1s_decisions:
        # V 1s mode použijeme current_time parameter
        cur_timestamp = current_time
    else:
        # V 5m mode použijeme df_features index
        cur_timestamp = df_features.index[current_step_index]
    
    cur_hour = cur_timestamp.hour
    dow = cur_timestamp.dayofweek        # 0=Mon … 6=Sun

    time_feats = [
        np.sin(cur_hour / 24 * 2 * np.pi),
        np.cos(cur_hour / 24 * 2 * np.pi),
        np.sin(dow / 7 * 2 * np.pi),
        np.cos(dow / 7 * 2 * np.pi),
    ]

    # --- Spojenie všetkých častí (vrátane time_feats) ---
    state_vector = np.concatenate((
        frame_flat_clipped,           # 1440 prvkov (48 features × 30 lookback)
        [
            float(current_pos),       # 1
            pnl_norm,                 # 2
            trade_duration,           # 3
            profit_status,            # 4
            tp_distance,              # 5
            sl_distance,              # 6
            pnl_atr_ratio             # 7
        ],
        time_feats                   # 4 time features
    )).astype(np.float32)             # Celkom 1440 + 7 + 4 = 1451 prvkov

    return state_vector

def run_backtest(config: dict, data_dict: dict, agent, vecnorm=None, use_1s_decisions: bool = False):
    """Spustí samotnú simuláciu obchodovania - verná kópia tréningového prostredia."""
    # --- Načítanie parametrov (ako v ScalpingEnv) ---
    initial_equity = config.get('account', {}).get('initialEquity', 10000)
    try:
        feature_cols = config['envSettings']['feature_columns']
        lookback = config['envSettings']['state_lookback']
        inactivity_limit = config['envSettings'].get('inactivity_limit', 600)
        max_ep_len = config['envSettings'].get('max_ep_len', 3_000)
    except KeyError as e: raise ValueError(f"Chýba kľúč v envSettings: {e}")

    trade_params = config.get('tradeParams', {})
    fee_perc = trade_params.get('feePercentage', 0.0) / 100.0
    slippage_perc = trade_params.get('slippagePercentage', 0.0) / 100.0
    min_sl_dist_perc = trade_params.get('minSLDistancePercent', 0.0) / 100.0
    min_sl_dist_atr_mult = trade_params.get('minSLDistanceATR', 1.0)
    min_atr_perc_price_threshold = trade_params.get('minAtrPercentOfPrice', 0.0) / 100.0
    rr_target = trade_params.get('rrTarget', 2.0)  # <<<< KĽÚČOVÝ PARAMETER
    
    # Asymetrické entry thresholds - ako v env
    entry_thr = trade_params.get('entryActionThreshold', 0.5)
    long_entry_thr = trade_params.get('longEntryThreshold', entry_thr)
    short_entry_thr = trade_params.get('shortEntryThreshold', entry_thr)
    exit_thr = trade_params.get('exitActionThreshold', 0.4)
    
    log.info(f"Entry thresholds - Long: {long_entry_thr}, Short: {short_entry_thr}, Exit: {exit_thr}")
    log.info(f"Risk-Reward Target: {rr_target}")

    risk_mgmt = config.get('riskManagement', {})
    position_sizing_method = risk_mgmt.get('positionSizingMethod', 'RiskPercentage')
    risk_per_trade = risk_mgmt.get('riskPerTradePercentage', 0.0) / 100.0
    max_pos_perc_equity = risk_mgmt.get('maxPositionSizePercentEquity', 100.0) / 100.0

    tsl_config = config.get('trailingStopLoss', {})
    tsl_enabled = tsl_config.get('enabled', False)
    tsl_activate_atr_mult = tsl_config.get('activateATRMultiplier', 1.0)  # Ako v env
    tsl_trail_atr_mult = tsl_config.get('trailATRMultiplier', 0.8)  # Ako v env

    atr_col = config.get('runtime', {}).get('atr_column')
    has_imbalance_col = config.get('runtime', {}).get('has_imbalance', False)
    
    # >>> NOVÉ: Time-of-day blackout hours (ako v ScalpingEnv)
    DISALLOWED_HOURS = set(config.get('blackoutHours', [0, 1, 2, 3]))  # Default: 00:00-03:59 UTC
    
    # >>> NOVÉ: Reward structure pre sophisticated hold logic
    reward_structure = config.get('trainingSettings', {}).get('rewardStructure', {})
    
    # >>> NOVÉ: Check spread and volatility filters (ako v ScalpingEnv)
    spread_filter_enabled = config.get('spreadFilter', {}).get('enabled', False)
    volatility_filter_enabled = config.get('volatilityFilter', {}).get('enabled', False)

    # --- Načítanie agenta a jeho observation space ---
    EXPECTED_AGENT_INPUT_SIZE = None; agent_obs_low = -5.0; agent_obs_high = 5.0
    if hasattr(agent, 'observation_space'):
         if isinstance(agent.observation_space, (gym_spaces.spaces.Box if gym_spaces else None.__class__)):
              obs_shape = agent.observation_space.shape
              if len(obs_shape) == 1:
                  EXPECTED_AGENT_INPUT_SIZE = obs_shape[0]
                  # --- OPRAVA: Správne získanie low/high pre skalárne alebo vektorové hranice ---
                  if isinstance(agent.observation_space.low, np.ndarray):
                      agent_obs_low = agent.observation_space.low.min() # Najnižšia možná hodnota across all dimensions
                  else:
                      agent_obs_low = agent.observation_space.low
                  if isinstance(agent.observation_space.high, np.ndarray):
                      agent_obs_high = agent.observation_space.high.max() # Najvyššia možná hodnota across all dimensions
                  else:
                      agent_obs_high = agent.observation_space.high

                  log.info(f"Agent obs space: Box({agent_obs_low}, {agent_obs_high}, ({EXPECTED_AGENT_INPUT_SIZE},), float32)")
              else:
                  log.warning(f"Agent obs space má tvar {obs_shape}."); EXPECTED_AGENT_INPUT_SIZE = np.prod(obs_shape); log.warning(f"Predpokladaná veľkosť vstupu {EXPECTED_AGENT_INPUT_SIZE}.")
         else: log.warning(f"Agent obs space nie je Box ({type(agent.observation_space)}).")
    else: log.warning("Nepodarilo sa získať observation_space."); EXPECTED_AGENT_INPUT_SIZE = 1597; log.warning(f"Používam manuálnu veľkosť: {EXPECTED_AGENT_INPUT_SIZE} a hranice [{agent_obs_low}, {agent_obs_high}]")
    if EXPECTED_AGENT_INPUT_SIZE is None: raise ValueError("Nepodarilo sa určiť očakávanú veľkosť vstupu agenta.")

    # --- Stav simulácie (rozšírený ako v ScalpingEnv) ---
    equity_total = initial_equity  # Celková equity across episodes
    max_equity = initial_equity    # Max equity tracker
    equity_ep = 0.0               # Episode-specific equity
    position = 0; position_size = 0.0; entry_price = 0.0; entry_time = None; entry_fee = 0.0;
    current_sl_price = 0.0; current_tp_price = 0.0; current_tsl_price = 0.0; peak_price_in_trade = 0.0;
    open_idx_sim = -1
    cooldown_counter = 0
    trailing_active = False  # TSL activation flag
    entry_atr = 0.0         # ATR at entry
    
    # >>> NOVÉ: Daily risk management (ako v env)
    daily_pnl = 0.0
    daily_risk_used = 0.0
    daily_trade_count = 0
    last_day = None
    trading_allowed = True
    
    trades = []; equity_curve = []

    # --- Získanie dát z data_dict ---
    data_5m = data_dict['primary']
    data_1s = data_dict['second']
    has_1s_data = data_1s is not None
    use_1s_decisions = data_dict.get('use_1s_decisions', False)

    log.info("Začínam backtest..."); log.info(f"Počiatočný kapitál: ${equity_total:.2f}"); log.info(f"Obdobie: {data_5m.index.min()} -> {data_5m.index.max()}");
    log.info(f"Počet krokov 5m: {len(data_5m)}"); log.info(f"Počet krokov 1s: {len(data_1s) if has_1s_data else 0}");
    log.info(f"Lookback: {lookback}"); log.info(f"Agent očakáva vstup: {EXPECTED_AGENT_INPUT_SIZE} v [{agent_obs_low}, {agent_obs_high}]")

    # --- Príprava dát ---
    present_feature_cols = [col for col in feature_cols if col in data_5m.columns]
    if len(present_feature_cols) != len(feature_cols): log.warning(f"Používam {len(present_feature_cols)}/{len(feature_cols)} features.")

    # --- PRÍPRAVA PRE REAL-TIME FORWARD-FILL ---
    if use_1s_decisions:
        log.info("🚀 REAL-TIME FORWARD-FILL MODE: Agent robí rozhodnutia každú sekundu")
        log.info("❌ LOOK-AHEAD BIAS ELIMINATED: Forward-fill sa robí v každom kroku simulácie")
        
        # Pripravíme zdrojové 5m features a 1s timestamps
        features_5m = data_5m[present_feature_cols].copy()
        
        # Debug: Check data_1s before creating main_timestamps
        log.info(f"DEBUG: data_1s type: {type(data_1s)}")
        log.info(f"DEBUG: data_1s shape: {data_1s.shape}")
        log.info(f"DEBUG: data_1s index length: {len(data_1s.index)}")
        if len(data_1s) > 0:
            log.info(f"DEBUG: data_1s index range: {data_1s.index.min()} -> {data_1s.index.max()}")
            log.info(f"DEBUG: data_1s columns: {list(data_1s.columns)}")
        else:
            log.error("DEBUG: data_1s is empty!")
        
        main_timestamps = data_1s.index
        
        # V 1s mode NEBUDE predpripravený df_features - robí sa real-time!
        df_features = None  # Bude sa vytvárať dynamicky v každom kroku
        
        log.info(f"✅ Pripravené pre real-time forward-fill: {len(main_timestamps)} 1s timestamps, {len(features_5m)} 5m barov")
        
    else:
        # Pôvodný 5m mode
        log.info("📊 STANDARD 5M MODE: Agent robí rozhodnutia každých 5 minút")
        df_features = data_5m[present_feature_cols].copy()
        main_timestamps = data_5m.index
        features_5m = None  # Nepoužíva sa v 5m mode

    # --- Príprava pre hlavnú slučku ---
    if use_1s_decisions:
        num_features_from_data = len(present_feature_cols)
        main_length = len(main_timestamps)
        log.info(f"1s mode: {num_features_from_data} features, {main_length} timestamps")
        
        # Debug: Check why main_timestamps might be empty
        if main_length == 0:
            log.error("main_timestamps is empty in 1s mode!")
            log.error(f"main_timestamps type: {type(main_timestamps)}")
            log.error(f"second_df length: {len(second_df) if second_df is not None else 'None'}")
            if second_df is not None:
                log.error(f"second_df index range: {second_df.index.min()} -> {second_df.index.max()}")
                log.error(f"second_df shape: {second_df.shape}")
            log.error(f"present_feature_cols: {present_feature_cols}")
    else:
        num_features_from_data = df_features.shape[1]
        main_length = len(df_features)
        log.info(f"5m mode: {num_features_from_data} features, {main_length} timestamps")
    
    expected_flat_size_from_data = lookback * num_features_from_data
    log.info(f"Počet features z dát: {num_features_from_data}"); log.info(f"Veľkosť splošteného stavu z dát: {expected_flat_size_from_data}")

    # --- Hlavná slučka ---
    log.info(f"DEBUG: Počet features: {num_features_from_data}, očakávaný flat size: {expected_flat_size_from_data}")
    log.info(f"DEBUG: Agent očakáva vstup tvaru: ({1}, {EXPECTED_AGENT_INPUT_SIZE}) -- Rozdiel: {EXPECTED_AGENT_INPUT_SIZE - expected_flat_size_from_data}")
    
    for i in range(lookback - 1, main_length):
        current_df_idx = i
        if use_1s_decisions:
            current_time = main_timestamps[current_df_idx]
        else:
            current_time = df_features.index[current_df_idx]
        
        # Progress a PnL logging každých 100 krokov
        if i % 100 == 0:
            progress_pct = (i-lookback+1)/(main_length-lookback)*100 if main_length > lookback else 0
            current_unrealized_pnl = 0.0
            if position != 0:
                current_unrealized_pnl = position_size * (current_price_close - entry_price) * position
            
            total_pnl = equity_total - initial_equity + current_unrealized_pnl
            log.info(f"🔄 Krok {i}/{main_length-1}: {current_time} | Progress: {progress_pct:.1f}% | Total PnL: ${total_pnl:.2f} | Equity: ${equity_total:.2f} | Pos: {position}")
        
        # --- Price data access: 1s mode uses 1s prices, 5m mode uses 5m prices ---
        if use_1s_decisions:
            try:
                current_row = data_1s.loc[current_time]
                # Log iba každých 500 sekúnd namiesto každej sekundy
                if i % 500 == 0:
                    log.debug(f"1s mode: using 1s OHLC for {current_time}")
            except KeyError:
                log.warning(f"Čas {current_time} nenájdený v '1s dátach'. Skip krok {i}."); continue
        else:
            try:
                current_row = data_5m.loc[current_time]
            except KeyError:
                log.warning(f"❌ Čas {current_time} nenájdený v 'data_5m'. Skip krok {i}."); continue

        current_price_close = current_row['close']; current_price_high = current_row['high']; current_price_low = current_row['low']
        
        # --- ATR access: always from 5m data, even in 1s mode ---
        if use_1s_decisions and atr_col:
            # In 1s mode, find corresponding 5m bar for ATR (latest available without look-ahead)
            mask_5m_atr = data_5m.index <= current_time
            available_5m_atr = data_5m[mask_5m_atr]
            if not available_5m_atr.empty:
                current_atr = available_5m_atr.iloc[-1].get(atr_col, 0.0)
                current_atr = current_atr if pd.notna(current_atr) else 0.0
                # Log iba každých 1000 sekúnd namiesto každej sekundy
                if i % 1000 == 0:
                    log.debug(f"1s mode: ATR {current_atr:.6f} from 5m bar {available_5m_atr.index[-1]}")
            else:
                current_atr = 0.0
        else:
            # Standard 5m mode or no ATR column
            current_atr = current_row.get(atr_col, 0.0) if atr_col else 0.0
            current_atr = current_atr if pd.notna(current_atr) else 0.0

        # --- Kontrola platnosti ATR ---
        is_atr_valid_for_calc = False
        if current_atr > 0 and atr_col:
            min_atr_threshold_value = current_price_close * min_atr_perc_price_threshold
            if current_atr >= min_atr_threshold_value:
                is_atr_valid_for_calc = True
            else:
                if i % 100 == 0:  # Log occasionally
                    log.info(f"{current_time}: BLOCKED - ATR too low: {current_atr:.5f} < {min_atr_threshold_value:.5f} (threshold: {min_atr_perc_price_threshold*100:.1f}%)")

        # --- Intra-bar 1s SL/TP checking (iba v 5m mode) ---
        if not use_1s_decisions and position != 0 and has_1s_data and current_time < data_5m.index[-1]:
            try:
                # V 5m mode: kontrolujeme SL/TP na 1s dátach medzi 5m barmi
                next_time = data_5m.index[current_df_idx + 1] if current_df_idx + 1 < len(data_5m) else data_5m.index[-1]
                
                # Kontrola dostupnosti 1s dát v požadovanom rozsahu
                if current_time in data_1s.index and next_time <= data_1s.index.max():
                    second_data_slice = data_1s.loc[current_time:next_time].iloc[1:] # Vynecháme prvý bar, ktorý je zhodný s aktuálnym 5m barom

                    if not second_data_slice.empty:
                        log.debug(f"5m mode: Kontrolujem {len(second_data_slice)} 1s barov medzi {current_time} a {next_time}")

                        # Prejdeme všetky 1s bary a skontrolujeme SL/TP
                        for sec_idx, sec_row in second_data_slice.iterrows():
                            sec_high = sec_row['high']; sec_low = sec_row['low'] # sec_close = sec_row['close'] - nepoužívame

                            # Kontrola SL/TP/TSL na 1s dátach
                            if position == 1: # Long
                                # SL/TP priority logic like in training env
                                hit_sl_1s = current_sl_price > 0 and sec_low <= current_sl_price
                                hit_tp_1s = current_tp_price > 0 and sec_high >= current_tp_price
                                # SL has priority over TP
                                if hit_sl_1s and hit_tp_1s: hit_tp_1s = False
                                
                                if hit_sl_1s:
                                    exit_reason = "SL"; exit_price = current_sl_price
                                    log.debug(f"SL LONG aktivovaný na 1s bare {sec_idx}")
                                    break
                                elif hit_tp_1s:
                                    exit_reason = "TP"; exit_price = current_tp_price
                                    log.debug(f"TP LONG aktivovaný na 1s bare {sec_idx}")
                                    break

                                # Update TSL Long na 1s dátach (EXACTLY as in ScalpingEnv)
                                if tsl_enabled and current_atr > 0:
                                    # R-multiple based activation (exact copy from env)
                                    initial_risk = abs(entry_price - current_sl_price)
                                    profit = sec_high - entry_price
                                    r_mult = profit / initial_risk if initial_risk > 1e-9 else 0

                                    # Activate trailing when profit reaches tsl_activate_atr_mult R
                                    if not trailing_active and r_mult >= tsl_activate_atr_mult:
                                        trailing_active = True
                                        current_sl_price = sec_high - tsl_trail_atr_mult * current_atr  # Use current_atr
                                        peak_price_in_trade = sec_high
                                        log.debug(f"{sec_idx}: TSL LONG activated at {r_mult:.2f}R")
                                    elif trailing_active:
                                        # Simple trailing update like in env
                                        new_sl = sec_high - tsl_trail_atr_mult * current_atr
                                        if new_sl > current_sl_price:
                                            current_sl_price = new_sl
                                            log.debug(f"{sec_idx}: TSL LONG updated to {new_sl:.5f}")

                            elif position == -1: # Short
                                # SL/TP priority logic like in training env
                                hit_sl_1s = current_sl_price > 0 and sec_high >= current_sl_price
                                hit_tp_1s = current_tp_price > 0 and sec_low <= current_tp_price
                                # SL has priority over TP
                                if hit_sl_1s and hit_tp_1s: hit_tp_1s = False
                                
                                if hit_sl_1s:
                                    exit_reason = "SL"; exit_price = current_sl_price
                                    log.debug(f"SL SHORT aktivovaný na 1s bare {sec_idx}")
                                    break
                                elif hit_tp_1s:
                                    exit_reason = "TP"; exit_price = current_tp_price
                                    log.debug(f"TP SHORT aktivovaný na 1s bare {sec_idx}")
                                    break

                                # Update TSL Short na 1s dátach (EXACTLY as in ScalpingEnv)
                                if tsl_enabled and current_atr > 0:
                                    # R-multiple based activation (exact copy from env)
                                    initial_risk = abs(entry_price - current_sl_price)
                                    profit = entry_price - sec_low
                                    r_mult = profit / initial_risk if initial_risk > 1e-9 else 0

                                    # Activate trailing when profit reaches tsl_activate_atr_mult R
                                    if not trailing_active and r_mult >= tsl_activate_atr_mult:
                                        trailing_active = True
                                        current_sl_price = sec_low + tsl_trail_atr_mult * current_atr  # Use current_atr
                                        peak_price_in_trade = sec_low
                                        log.debug(f"{sec_idx}: TSL SHORT activated at {r_mult:.2f}R")
                                    elif trailing_active:
                                        # Simple trailing update like in env
                                        new_sl = sec_low + tsl_trail_atr_mult * current_atr
                                        if new_sl < current_sl_price:
                                            current_sl_price = new_sl
                                            log.debug(f"{sec_idx}: TSL SHORT updated to {new_sl:.5f}")
                else:
                    # 1s dáta nie sú dostupné v tomto rozsahu - pokračuj bez intra-bar checking
                    if i % 500 == 0:  # Log iba občas, aby sme nespamovali
                        log.info(f"{current_time}: 1s dáta nedostupné pre intra-bar SL/TP checking (pokračuje bez neho)")
                        
            except Exception as e:
                # Ak nastane chyba pri prístupe k 1s dátam, pokračuj bez intra-bar checking
                if i % 500 == 0:  # Log iba občas
                    log.warning(f"{current_time}: Chyba pri intra-bar 1s checking: {e} (pokračuje bez neho)")
        elif use_1s_decisions:
            # V 1s mode: SL/TP sa kontroluje priamo v hlavnej slučke, intra-bar checking nie je potrebný
            # Log iba raz za simuláciu namiesto každej sekundy
            if i == 0:
                log.debug(f"1s mode: SL/TP checking will be done in main loop, no intra-bar needed")

        # --- Kontrola platnosti ATR ---
        is_atr_valid_for_calc = False
        if current_atr > 0 and atr_col:
            min_atr_threshold_value = current_price_close * min_atr_perc_price_threshold
            if current_atr >= min_atr_threshold_value:
                is_atr_valid_for_calc = True
            # else: # Zrušil som logovanie, lebo môže byť veľmi časté
                 # log.debug(f"{current_time}: ATR ({current_atr:.5f}) je pod minimálnym prahom ({min_atr_threshold_value:.5f}), nebude použité pre SL/TSL.")

        # >>> NOVÉ: Daily risk management reset (ako v ScalpingEnv)
        cur_day = current_time.date()
        if last_day is None or cur_day != last_day:
            daily_pnl = 0.0
            daily_risk_used = 0.0
            daily_trade_count = 0
            trading_allowed = True
            last_day = cur_day
            log.debug(f"{current_time}: Nový deň, reset daily limits")

        # Stop trading if daily limits exceeded (configurable)
        risk_mgmt = config.get('riskManagement', {})
        max_daily_risk_r_mult = risk_mgmt.get('maxDailyRiskPercent', 999999.0)  # R-multiple limit (positive value)
        max_daily_trades = config.get('maxDailyTrades', 999999)  # Very high default for debug
        max_daily_atr_loss_multiplier = config.get('maxDailyAtrLossMultiplier', 999999.0)  # Very high default

        # Check if daily loss limits exceeded (negative R-multiple means loss)
        if (daily_risk_used <= -max_daily_risk_r_mult or
            (current_atr > 0 and daily_pnl <= -max_daily_atr_loss_multiplier * current_atr) or
            daily_trade_count >= max_daily_trades) and trading_allowed:
            trading_allowed = False
            log.warning(f"{current_time}: Daily limit hit - risk: {daily_risk_used:.2f}R (limit: -{max_daily_risk_r_mult:.2f}R), pnl: {daily_pnl:.2f}, trades: {daily_trade_count} (limit: {max_daily_trades})")
        
        # Debug daily status
        if i % 500 == 0:
            log.info(f"{current_time}: Daily status - trading_allowed: {trading_allowed}, risk_used: {daily_risk_used:.2f}, pnl: {daily_pnl:.2f}, trades: {daily_trade_count}")

        # --- Equity Curve ---
        current_step_equity = equity_total
        if position != 0:
            unrealized_pnl = position_size * (current_price_close - entry_price) * position
            current_step_equity += unrealized_pnl
        equity_curve.append({'timestamp': current_time, 'equity': current_step_equity})
        
        # Update max equity
        max_equity = max(max_equity, current_step_equity)

        exit_reason = None; exit_price = 0.0; action_raw = np.zeros(4)

        # --- Cooldown ---
        if cooldown_counter > 0:
            cooldown_counter -= 1
            # Skip all trading logic if in cooldown
            if cooldown_counter > 0:
                continue

        # --- Kontrola SL/TP/TSL (EXACTLY as in ScalpingEnv) ---
        if position == 1: # Long
            hit_sl = current_sl_price > 0 and current_price_low <= current_sl_price
            hit_tp = current_tp_price > 0 and current_price_high >= current_tp_price
            # SL has priority over TP (exactly like in env)
            if hit_sl and hit_tp: hit_tp = False
            
            if hit_sl: exit_reason = "SL"; exit_price = current_sl_price
            elif hit_tp: exit_reason = "TP"; exit_price = current_tp_price

            # Update TSL Long (EXACTLY as in ScalpingEnv - using current ATR)
            if exit_reason is None and tsl_enabled and current_atr > 0:
                # R-multiple based activation (exact copy from env)
                initial_risk = abs(entry_price - current_sl_price)
                profit = current_price_high - entry_price
                r_mult = profit / initial_risk if initial_risk > 1e-9 else 0

                # Activate trailing when profit reaches tsl_activate_atr_mult R
                if not trailing_active and r_mult >= tsl_activate_atr_mult:
                    trailing_active = True
                    current_sl_price = current_price_high - tsl_trail_atr_mult * current_atr  # Use current_atr like env
                    peak_price_in_trade = current_price_high
                    log.debug(f"{current_time}: TSL LONG activated at {r_mult:.2f}R")
                elif trailing_active:
                    # Simple trailing update (second update like in env)
                    new_sl = current_price_high - tsl_trail_atr_mult * current_atr
                    if new_sl > current_sl_price:
                        current_sl_price = new_sl
                        log.debug(f"{current_time}: TSL LONG updated to {new_sl:.5f}")

        elif position == -1: # Short
            hit_sl = current_sl_price > 0 and current_price_high >= current_sl_price
            hit_tp = current_tp_price > 0 and current_price_low <= current_tp_price
            # SL has priority over TP (exactly like in env)
            if hit_sl and hit_tp: hit_tp = False
            
            if hit_sl: exit_reason = "SL"; exit_price = current_sl_price
            elif hit_tp: exit_reason = "TP"; exit_price = current_tp_price

            # Update TSL Short (EXACTLY as in ScalpingEnv - using current ATR)
            if exit_reason is None and tsl_enabled and current_atr > 0:
                 # R-multiple based activation (exact copy from env)
                 initial_risk = abs(entry_price - current_sl_price)
                 profit = entry_price - current_price_low
                 r_mult = profit / initial_risk if initial_risk > 1e-9 else 0

                 # Activate trailing when profit reaches tsl_activate_atr_mult R
                 if not trailing_active and r_mult >= tsl_activate_atr_mult:
                     trailing_active = True
                     current_sl_price = current_price_low + tsl_trail_atr_mult * current_atr  # Use current_atr like env
                     peak_price_in_trade = current_price_low
                     log.debug(f"{current_time}: TSL SHORT activated at {r_mult:.2f}R")
                 elif trailing_active:
                     # Simple trailing update (second update like in env)
                     new_sl = current_price_low + tsl_trail_atr_mult * current_atr
                     if new_sl < current_sl_price:
                         current_sl_price = new_sl
                         log.debug(f"{current_time}: TSL SHORT updated to {new_sl:.5f}")

        # --- Ak nastal SL/TP/TSL exit ---
        if exit_reason:
            sim_exit_price = exit_price
            sim_exit_price = sim_exit_price * (1 - slippage_perc * position) # Aplikuj slippage
            log.info(f"{current_time}: EXIT {position_size:.4f} u {'L' if position == 1 else 'S'} @ {sim_exit_price:.5f} (R: {exit_reason}, Trig: {exit_price:.5f})")
            pnl = position_size * (sim_exit_price - entry_price) * position
            exit_fee = abs(position_size * sim_exit_price * fee_perc)
            net_pnl = pnl - entry_fee - exit_fee
            equity_total += pnl - exit_fee
            
            # Daily risk tracking
            initial_risk = abs(entry_price - current_sl_price)
            r_mult = net_pnl / (initial_risk * position_size) if initial_risk > 1e-9 else 0
            daily_risk_used += r_mult
            daily_pnl += net_pnl
            
            trades.append({
                'entry_time': entry_time, 'exit_time': current_time,
                'direction': 'Long' if position == 1 else 'Short',
                'size': position_size, 'entry_price': entry_price, 'exit_price': sim_exit_price,
                'pnl': net_pnl, 'exit_reason': exit_reason, 'entry_fee': entry_fee, 'exit_fee': exit_fee
            })
            log.info(f" PnL: {net_pnl:.2f}, Fee: {exit_fee:.4f}, EQ: ${equity_total:.2f}")
            
            # >>> NOVÉ: Cooldown after SL (ako v ScalpingEnv)
            if exit_reason.startswith("SL"):
                cooldown_counter = 60  # 60 second cooldown after stop loss
                log.debug(f"SL hit, starting cooldown: {cooldown_counter}s")
            elif exit_reason == "TP":
                cooldown_counter = 3  # Short cooldown after TP
            
            # Reset stavu
            position = 0; position_size = 0.0; entry_price = 0.0; entry_time = None; entry_fee = 0.0
            current_sl_price = 0.0; current_tp_price = 0.0; current_tsl_price = 0.0; peak_price_in_trade = 0.0
            trailing_active = False; entry_atr = 0.0; open_idx_sim = -1


        # --- Získanie a interpretácia akcie agenta (ak sme stále v pozícii alebo flat) ---
        if exit_reason is None:
            try:
                 # Real-time forward-fill volanie pre 1s mode alebo štandardné pre 5m mode
                 state_vector = get_state(
                     df_features=df_features,
                     current_step_index=current_df_idx,
                     lookback=lookback,
                     current_pos=position,
                     entry_price=entry_price,
                     current_price=current_price_close,
                     current_sl_price=current_sl_price,
                     current_tp_price=current_tp_price,
                     open_idx_sim=open_idx_sim,
                     current_atr=current_atr if is_atr_valid_for_calc else 0.0,
                     inactivity_limit=inactivity_limit,
                     tsl_enabled=tsl_enabled,
                     use_1s_decisions=use_1s_decisions,
                     features_5m=features_5m,
                     main_timestamps=main_timestamps,
                     current_time=current_time
                 )

                 state_input = np.expand_dims(state_vector, axis=0)
                 
                 # Aplikuj VecNormalize ak je dostupné
                 if vecnorm is not None:
                     try:
                         state_input = vecnorm.normalize_obs(state_input)
                         # Log iba každých 2000 sekúnd namiesto každej sekundy
                         if i % 2000 == 0:
                             log.debug(f"{current_time}: VecNormalize aplikované na state")
                     except Exception as e:
                         log.warning(f"{current_time}: Chyba pri aplikácii VecNormalize: {e}")
                 
                 state_clipped = np.clip(state_input, agent_obs_low, agent_obs_high).astype(np.float32)

                 # Kontrola tvaru PRED volaním predict
                 if state_clipped.shape != (1, EXPECTED_AGENT_INPUT_SIZE):
                     log.error(f"NEZHODA TVARU PRED PREDICT! Očakávaný: {(1, EXPECTED_AGENT_INPUT_SIZE)}, Aktuálny: {state_clipped.shape}! Skip krok.")
                     continue

                 action_raw, _ = agent.predict(state_clipped, deterministic=True)
                 if not isinstance(action_raw, np.ndarray) or action_raw.size < 4: log.error(f"Predict vrátil nečakaný tvar/veľkosť: {action_raw}. Skip."); continue
                 action_clipped = np.clip(action_raw.flatten(), -1.0, 1.0)
                 entry_sig = action_clipped[0]; a_tp = action_clipped[2]; exit_sig = action_clipped[3] # a_sl = action_clipped[1] - nepoužívame
                 
                 # Debug výstup každých 100 krokov
                 if i % 100 == 0:
                     log.info(f"{current_time}: Actions: entry={entry_sig:.4f}, exit={exit_sig:.4f}, pos={position}, state_shape={state_clipped.shape}")

            except Exception as e: log.error(f"Chyba pri predict/príprave stavu v kroku {i} ({current_time}): {e}", exc_info=True); continue

            # --- Spracovanie Akcie ---
            # 1. EXIT signálom agenta
            if position != 0:
                 # >>> NOVÉ: Minimum time in trade (ako v ScalpingEnv)
                 time_in_trade = current_df_idx - open_idx_sim
                 min_time_required = 30  # 30 seconds
                 
                 exit_sig_long_triggered = (exit_sig > exit_thr and position == 1)
                 exit_sig_short_triggered = (exit_sig < -exit_thr and position == -1)
                 
                 # >>> NOVÉ: Soft TP logic (ako v ScalpingEnv)
                 soft_tp_triggered = False
                 if (exit_sig_long_triggered or exit_sig_short_triggered) and is_atr_valid_for_calc:
                     profit = (current_price_close - entry_price) * position
                     atr_threshold = 1.5 * current_atr  # Soft TP when profit > 1.5*ATR
                     if profit > atr_threshold:
                         soft_tp_triggered = True
                         log.debug(f"{current_time}: Soft TP triggered, profit: {profit:.5f} > {atr_threshold:.5f}")
                 
                 # Check minimum time constraint (unless soft TP)
                 if (exit_sig_long_triggered or exit_sig_short_triggered) and not soft_tp_triggered:
                     if time_in_trade < min_time_required:
                         log.debug(f"{current_time}: Min time hold: {time_in_trade}s < {min_time_required}s required")
                         # Don't exit yet, continue to rest of logic
                     else:
                         # Execute exit
                         sim_exit_price = current_price_close * (1 - slippage_perc * position)
                         is_profit = (sim_exit_price - entry_price) * position > 0
                         exit_reason = "TP_SIG" if is_profit else "SL_SIG"
                         
                         log.info(f"{current_time}: EXIT {'LONG' if position==1 else 'SHORT'} {position_size:.4f} @ {sim_exit_price:.5f} (Agent signal {exit_sig:.3f})")
                         pnl = position_size * (sim_exit_price - entry_price) * position
                         exit_fee = abs(position_size * sim_exit_price * fee_perc)
                         net_pnl = pnl - entry_fee - exit_fee
                         equity_total += pnl - exit_fee
                         
                         # Daily risk tracking
                         initial_risk = abs(entry_price - current_sl_price)
                         r_mult = net_pnl / (initial_risk * position_size) if initial_risk > 1e-9 else 0
                         daily_risk_used += r_mult
                         daily_pnl += net_pnl
                         
                         trades.append({
                             'entry_time': entry_time, 'exit_time': current_time,
                             'direction': 'Long' if position == 1 else 'Short',
                             'size': position_size, 'entry_price': entry_price, 'exit_price': sim_exit_price,
                             'pnl': net_pnl, 'exit_reason': exit_reason, 'entry_fee': entry_fee, 'exit_fee': exit_fee
                         })
                         log.info(f" PnL: {net_pnl:.2f}, Fee: {exit_fee:.4f}, EQ: ${equity_total:.2f}")
                         
                         # Reset stavu
                         position = 0; position_size = 0.0; entry_price = 0.0; entry_time = None; entry_fee = 0.0
                         current_sl_price = 0.0; current_tp_price = 0.0; current_tsl_price = 0.0; peak_price_in_trade = 0.0
                         trailing_active = False; entry_atr = 0.0; open_idx_sim = -1
                         continue
                 
                 # Soft TP execution
                 elif soft_tp_triggered:
                     sim_exit_price = current_price_close * (1 - slippage_perc * position)
                     log.info(f"{current_time}: SOFT TP EXIT {'LONG' if position==1 else 'SHORT'} {position_size:.4f} @ {sim_exit_price:.5f}")
                     pnl = position_size * (sim_exit_price - entry_price) * position
                     exit_fee = abs(position_size * sim_exit_price * fee_perc)
                     net_pnl = pnl - entry_fee - exit_fee
                     equity_total += pnl - exit_fee
                     
                     # Daily risk tracking
                     initial_risk = abs(entry_price - current_sl_price)
                     r_mult = net_pnl / (initial_risk * position_size) if initial_risk > 1e-9 else 0
                     daily_risk_used += r_mult
                     daily_pnl += net_pnl
                     
                     trades.append({
                         'entry_time': entry_time, 'exit_time': current_time,
                         'direction': 'Long' if position == 1 else 'Short',
                         'size': position_size, 'entry_price': entry_price, 'exit_price': sim_exit_price,
                         'pnl': net_pnl, 'exit_reason': 'SOFT_TP', 'entry_fee': entry_fee, 'exit_fee': exit_fee
                     })
                     log.info(f" PnL: {net_pnl:.2f}, Fee: {exit_fee:.4f}, EQ: ${equity_total:.2f}")
                     
                     # Reset stavu
                     position = 0; position_size = 0.0; entry_price = 0.0; entry_time = None; entry_fee = 0.0
                     current_sl_price = 0.0; current_tp_price = 0.0; current_tsl_price = 0.0; peak_price_in_trade = 0.0
                     trailing_active = False; entry_atr = 0.0; open_idx_sim = -1
                     continue

            # 2. ENTRY signálom agenta (iba ak sme flat a nie sme v cooldown)
            if position == 0:
                # Debug blocking conditions
                if i % 100 == 0:
                    log.info(f"{current_time}: Entry conditions - position: {position}, cooldown: {cooldown_counter}, trading_allowed: {trading_allowed}")
                
                if cooldown_counter > 0:
                    if i % 100 == 0:
                        log.info(f"{current_time}: BLOCKED - Cooldown active ({cooldown_counter}s remaining)")
                    continue
                
                if not trading_allowed:
                    if i % 100 == 0:
                        log.info(f"{current_time}: BLOCKED - Trading not allowed (daily limits)")
                    continue
                # >>> NOVÉ: Time-of-day blackout check (ako v ScalpingEnv)
                current_hour = current_time.hour
                if current_hour in DISALLOWED_HOURS:
                    # Skip entry during blackout hours
                    if i % 100 == 0:  # Log occasionally to avoid spam
                        log.info(f"{current_time}: BLOCKED - Time blackout (hour {current_hour})")
                    continue
                
                triggered_pos = 0
                if entry_sig > long_entry_thr: triggered_pos = 1
                elif entry_sig < -short_entry_thr: triggered_pos = -1

                # Debug entry signal strength
                if i % 100 == 0:
                    log.info(f"{current_time}: Entry signal: {entry_sig:.4f}, Long thresh: {long_entry_thr}, Short thresh: {short_entry_thr}, triggered_pos: {triggered_pos}")

                if triggered_pos != 0:
                    sim_entry_price = current_price_close * (1 + slippage_perc * triggered_pos) # Aplikuj slippage

                    # --- Upravený výpočet SL a TP ---
                    sl_dist_perc_points = max(sim_entry_price * min_sl_dist_perc, current_price_close * 0.0005)
                    sl_dist_atr_points = 0.0
                    if is_atr_valid_for_calc: sl_dist_atr_points = current_atr * min_sl_dist_atr_mult

                    if is_atr_valid_for_calc and sl_dist_atr_points > 0:
                         min_sl_dist_points = max(sl_dist_perc_points, sl_dist_atr_points)
                         log.info(f"{current_time}: SL distance based on MAX(Perc: {sl_dist_perc_points:.5f}, ATR: {sl_dist_atr_points:.5f}) = {min_sl_dist_points:.5f}")
                    else:
                         min_sl_dist_points = sl_dist_perc_points
                         log.info(f"{current_time}: Using Perc-only SL: {min_sl_dist_points:.5f} (ATR invalid: {current_atr:.5f})")
                         # Continue with percentage-based stop loss instead of blocking

                    if min_sl_dist_points <= 1e-9:
                        log.warning(f"{current_time}: BLOCKED - Zero SL distance for {'LONG' if triggered_pos==1 else 'SHORT'}. Skip.")
                        continue

                    # >>> TP calculation EXACTLY as in ScalpingEnv
                    tp_raw = rr_target + a_tp  # a_tp ∈ [-1,1]
                    tp_raw = max(tp_raw, 1.0)  # Ensure minimum 1R targets
                    tp_raw = np.clip(tp_raw, 1.0, 4.0)  # CRITICAL: Clip between 1.0-4.0 R like in env
                    tp_dist_points = min_sl_dist_points * tp_raw

                    sl_price = sim_entry_price - min_sl_dist_points if triggered_pos == 1 else sim_entry_price + min_sl_dist_points
                    tp_price = sim_entry_price + tp_dist_points if triggered_pos == 1 else sim_entry_price - tp_dist_points

                    # --- Výpočet veľkosti pozície ---
                    if position_sizing_method == 'RiskPercentage':
                        risk_amount = equity_total * risk_per_trade
                        size_in_units = risk_amount / min_sl_dist_points if min_sl_dist_points > 1e-9 else 0
                    else: log.error(f"Neznámy sizing: {position_sizing_method}"); continue

                    # >>> NOVÉ: Signal strength-based position sizing (ako v ScalpingEnv)
                    signal_strength = abs(entry_sig)
                    size_multiplier = 0.5 + 0.5 * min(signal_strength / 1.0, 1.0)  # Scale from 0.5x to 1.0x based on signal strength
                    size_in_units *= size_multiplier

                    # Dynamický boost
                    size_boost = 1.0
                    if has_imbalance_col:
                         imb = current_row.get("depth_imbalance5", 0.0)
                         if abs(imb) > 0.3: size_boost = 1.5
                    size_in_units *= size_boost

                    # Kontrola max veľkosti
                    max_size_by_equity = (equity_total * max_pos_perc_equity) / sim_entry_price if sim_entry_price > 0 else 0
                    if max_size_by_equity > 0: size_in_units = min(size_in_units, max_size_by_equity)

                    if size_in_units <= 1e-9: log.warning(f"{current_time}: Nulová veľkosť {'LONG' if triggered_pos==1 else 'SHORT'}. Skip."); continue

                    # --- Simulácia vstupu ---
                    current_entry_fee = size_in_units * sim_entry_price * fee_perc
                    if equity_total - current_entry_fee <= 0: log.warning(f"{current_time}: Nedostatok EQ ({equity_total:.2f}) pre poplatok ({current_entry_fee:.2f}) {'LONG' if triggered_pos==1 else 'SHORT'}."); continue

                    equity_total -= current_entry_fee
                    position = triggered_pos; position_size = size_in_units; entry_price = sim_entry_price;
                    current_sl_price = sl_price; current_tp_price = tp_price; current_tsl_price = 0.0; peak_price_in_trade = entry_price;
                    entry_time = current_time; entry_fee = current_entry_fee;
                    open_idx_sim = current_df_idx # <<<<<<<<<<<<<<<<<<<<<<< ULOŽÍME INDEX OTVORENIA
                    entry_atr = current_atr  # Store ATR at entry for TSL calculations
                    trailing_active = False  # Reset TSL state
                    
                    # >>> NOVÉ: Increment daily trade count (ako v ScalpingEnv)
                    daily_trade_count += 1

                    log.info(f"{current_time}: ENTER {'LONG' if position==1 else 'SHORT'} {position_size:.4f} @ {entry_price:.5f}, SL: {sl_price:.5f}, TP: {tp_price:.5f}, Fee: {entry_fee:.4f}, EQ: ${equity_total:.2f}")


    # --- Koniec slučky - uzavretie poslednej pozície ---
    if position != 0:
        if use_1s_decisions:
            last_time = data_1s.index[-1]
            last_price = data_1s['close'].iloc[-1]
            log.warning(f"{last_time}: Vynútené uzavretie pozície na konci (1s mode).")
        else:
            last_time = data_5m.index[-1]
            last_price = data_5m['close'].iloc[-1]
            log.warning(f"{last_time}: Vynútené uzavretie pozície na konci (5m mode).")
        exit_price = last_price * (1 - slippage_perc * position)
        pnl = position_size * (exit_price - entry_price) * position
        exit_fee = abs(position_size * exit_price * fee_perc)
        net_pnl = pnl - entry_fee - exit_fee
        equity_total += pnl - exit_fee
        
        # Daily risk tracking
        initial_risk = abs(entry_price - current_sl_price)
        r_mult = net_pnl / (initial_risk * position_size) if initial_risk > 1e-9 else 0
        daily_risk_used += r_mult
        daily_pnl += net_pnl
        
        trades.append({
            'entry_time': entry_time, 'exit_time': last_time,
            'direction': 'Long' if position == 1 else 'Short',
            'size': position_size, 'entry_price': entry_price, 'exit_price': exit_price,
            'pnl': net_pnl, 'exit_reason': 'End of Data', 'entry_fee': entry_fee, 'exit_fee': exit_fee
        })
        log.info(f" Uzavretá pozícia: {position_size:.4f} u {'L' if position == 1 else 'S'} @ {exit_price:.5f}")
        log.info(f" PnL: {net_pnl:.2f}, Fee: {exit_fee:.4f}")

    log.info(f" Finálna Equity: ${equity_total:.2f}")
    log.info("Backtest dokončený.")
    
    # Handle empty equity_curve case
    if equity_curve:
        equity_curve_df = pd.DataFrame(equity_curve).set_index('timestamp')
    else:
        # Create empty DataFrame with proper structure
        equity_curve_df = pd.DataFrame(columns=['equity'])
        equity_curve_df.index.name = 'timestamp'
        log.warning("Equity curve is empty - no steps were executed in the simulation")
    
    return trades, equity_curve_df, equity_total

def calculate_metrics(trades_df: pd.DataFrame, equity_curve: pd.DataFrame, initial_equity: float, final_equity: float, start_time, end_time):
    """Vypočíta základné metriky výkonnosti."""
    metrics = {}; metrics["Obdobie"] = f"{start_time.date()} - {end_time.date()}"; metrics["Počiatočný kapitál"] = initial_equity; metrics["Konečný kapitál"] = final_equity
    metrics["Celkový PnL ($)"] = final_equity - initial_equity; metrics["Celkový PnL (%)"] = (final_equity / initial_equity - 1) * 100 if initial_equity > 1e-9 else 0

    if trades_df.empty:
        log.warning("Neboli vykonané žiadne obchody."); metrics["Počet obchodov"] = 0; [metrics.update({k: 0}) for k in ["Počet ziskových obchodov", "Počet stratových obchodov", "Win Rate (%)", "Priemerný zisk ($)", "Priemerná strata ($)", "Priemerný PnL na obchod ($)", "Najväčší zisk ($)", "Najväčšia strata ($)", "Celkové poplatky ($)", "Profit Factor", "Risk/Reward Ratio (Avg)", "Maximálny Drawdown ($)", "Maximálny Drawdown (%)"]]; [metrics.update({k: "N/A"}) for k in ["Ročný výnos (%)", "Calmar Ratio (approx)"]]; return metrics

    metrics["Počet obchodov"] = len(trades_df); wins = trades_df[trades_df['pnl'] > 0]; losses = trades_df[trades_df['pnl'] <= 0]
    metrics["Počet ziskových obchodov"] = len(wins); metrics["Počet stratových obchodov"] = len(losses); metrics["Win Rate (%)"] = (len(wins) / len(trades_df)) * 100 if len(trades_df) > 0 else 0
    metrics["Priemerný zisk ($)"] = wins['pnl'].mean() if len(wins) > 0 else 0; metrics["Priemerná strata ($)"] = losses['pnl'].mean() if len(losses) > 0 else 0
    avg_loss_abs = abs(metrics["Priemerná strata ($)"]) if len(losses) > 0 else 0; metrics["Priemerný PnL na obchod ($)"] = trades_df['pnl'].mean()
    metrics["Najväčší zisk ($)"] = trades_df['pnl'].max(); metrics["Najväčšia strata ($)"] = trades_df['pnl'].min()
    total_fees = trades_df['entry_fee'].sum() + trades_df['exit_fee'].sum(); metrics["Celkové poplatky ($)"] = total_fees
    sum_wins = wins['pnl'].sum(); sum_losses = abs(losses['pnl'].sum())
    metrics["Profit Factor"] = (sum_wins / sum_losses) if sum_losses > 1e-9 else (np.inf if sum_wins > 1e-9 else 0)
    metrics["Risk/Reward Ratio (Avg)"] = (abs(metrics["Priemerný zisk ($)"]) / avg_loss_abs) if avg_loss_abs > 1e-9 else (np.inf if abs(metrics["Priemerný zisk ($)"]) > 1e-9 else 0)

    if not equity_curve.empty:
        equity_curve['peak'] = equity_curve['equity'].cummax(); equity_curve['drawdown'] = equity_curve['equity'] - equity_curve['peak']
        equity_curve['drawdown_pct'] = np.where( equity_curve['peak'] > 1e-9, (equity_curve['equity'] / equity_curve['peak'] - 1) * 100, 0.0 )
        metrics["Maximálny Drawdown ($)"] = equity_curve['drawdown'].min() if not equity_curve.empty else 0
        metrics["Maximálny Drawdown (%)"] = equity_curve['drawdown_pct'].min() if not equity_curve.empty else 0
        duration_days = (end_time - start_time).total_seconds() / (24 * 3600)
        total_return = (final_equity / initial_equity) - 1 if initial_equity > 1e-9 else 0
        
        # Anualizácia má zmysel iba pre obdobia dlhšie ako 30 dní
        MINIMUM_DAYS_FOR_ANNUALIZATION = 30
        
        if duration_days >= MINIMUM_DAYS_FOR_ANNUALIZATION:
            duration_years = duration_days / 365.25
            if duration_years > 0 and total_return > -1:
                annualized_return = (1 + total_return)**(1 / duration_years) - 1
                metrics["Ročný výnos (%)"] = annualized_return * 100
                max_drawdown_abs_pct = abs(metrics["Maximálny Drawdown (%)"])
                metrics["Calmar Ratio (approx)"] = ((annualized_return * 100) / max_drawdown_abs_pct) if max_drawdown_abs_pct > 1e-9 else (np.inf if annualized_return > 1e-9 else 0)
            else:
                log.warning("Neplatné hodnoty pre anualizáciu (duration_years <= 0 alebo total_return <= -1)")
                metrics["Ročný výnos (%)"] = "N/A"
                metrics["Calmar Ratio (approx)"] = "N/A"
        else:
            log.info(f"Trvanie backtestu ({duration_days:.1f} dní) je príliš krátke pre anualizáciu (min. {MINIMUM_DAYS_FOR_ANNUALIZATION} dní)")
            # Pre krátke obdobia poskytujeme alternatívne metriky
            daily_return = total_return / duration_days if duration_days > 0 else 0
            weekly_return = daily_return * 7
            monthly_return = daily_return * 30
            
            metrics["Denný výnos (%)"] = daily_return * 100
            metrics["Týždenný výnos (%) odhad"] = weekly_return * 100
            metrics["Mesačný výnos (%) odhad"] = monthly_return * 100
            metrics["Ročný výnos (%)"] = "N/A (krátke obdobie)"
            metrics["Calmar Ratio (approx)"] = "N/A (krátke obdobie)"
    else: log.warning("Equity krivka prázdna."); [metrics.update({k: 0}) for k in ["Maximálny Drawdown ($)", "Maximálny Drawdown (%)"]]; [metrics.update({k: "N/A"}) for k in ["Ročný výnos (%)", "Calmar Ratio (approx)"]];

    return metrics

# --- Hlavná časť skriptu ---
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Spustí backtest pre natrénovaného agenta.")
    parser.add_argument("--cfg", required=True, type=Path, help="Cesta ku konfiguračnému súboru (YAML/JSON).")
    parser.add_argument("--start", required=True, help="Dátum začiatku backtestu (YYYY-MM-DD).")
    parser.add_argument("--end", required=True, help="Dátum konca backtestu (YYYY-MM-DD, vrátane).")
    parser.add_argument("--model", type=Path, default=None, help="Voliteľná cesta k modelu agenta (prepíše cestu v configu).")
    parser.add_argument("--out-trades", type=Path, default="backtest_trades.csv", help="Súbor pre uloženie zoznamu obchodov.")
    parser.add_argument("--out-equity", type=Path, default="backtest_equity.csv", help="Súbor pre uloženie krivky kapitálu.")
    parser.add_argument("--log-level", default="INFO", choices=["DEBUG", "INFO", "WARNING", "ERROR"], help="Úroveň logovania.")
    parser.add_argument("--use-1s-decisions", action="store_true", help="Agent robí rozhodnutia každú sekundu namiesto každých 5 minút (experimentálne).")
    args = parser.parse_args()

    # --- Nastavenie Loggera ---
    for handler in log.handlers[:]: log.removeHandler(handler) # Odstráni existujúce handlery
    log.setLevel(args.log_level.upper())
    handler = logging.StreamHandler()
    # Pridal som meno loggera do formátu pre lepšiu identifikáciu
    formatter = logging.Formatter("%(asctime)s [%(name)s] [%(levelname)s] %(message)s", datefmt="%Y-%m-%d %H:%M:%S")
    handler.setFormatter(formatter)
    log.addHandler(handler)
    log.info(f"Logging nastavený na úroveň: {args.log_level.upper()}")
    log.info(f"Použitá knižnica pre gym.spaces: {log_gym_lib}")


    try:
        config = load_config(args.cfg)
        
        # Automatická detekcia model a vecnorm súborov
        if args.model:
            model_path = Path(args.model)
        else:
            model_path = config.get('runtime', {}).get('model_path')
            if not model_path:
                raise ValueError("Cesta k modelu nie je definovaná ani v args ani v configu.")
            model_path = Path(model_path)
        
        # Ak model_path neexistuje ako .zip, skúsme nájsť checkpoint súbory
        if not model_path.exists():
            # Skúsme nájsť posledný checkpoint v chk/ adresári
            chk_dir = Path("./chk")
            if chk_dir.exists():
                # Nájdeme všetky .zip súbory v chk/
                model_files = list(chk_dir.glob("sac_*_steps.zip"))
                if model_files:
                    # Sortujeme podľa timesteps a vezmeme posledný
                    model_files.sort(key=lambda x: int(x.stem.split('_')[1]))
                    model_path = model_files[-1]
                    log.info(f"Auto-detekovaný model checkpoint: {model_path}")
        
        if not model_path.exists():
            log.error(f"Model neexistuje: {model_path}")
            exit(1)
            
        # Nájdenie zodpovedajúceho VecNormalize súboru
        vecnorm_path = model_path.with_suffix('.vecnorm.pkl')
        if not vecnorm_path.exists():
            # Skúsme alternatívny formát názvu
            model_stem = model_path.stem.replace('_steps', '')
            vecnorm_path = model_path.parent / f"{model_stem}.vecnorm.pkl"
            
        if not vecnorm_path.exists():
            log.warning(f"VecNormalize súbor nenájdený: {vecnorm_path}. Pozorování nebudú normalizované!")
            vecnorm = None
        else:
            log.info(f"Načítavam VecNormalize: {vecnorm_path}")
            try:
                from stable_baselines3.common.vec_env import DummyVecEnv
                import gymnasium as gym
                
                # Vytvoríme dummy environment pre načítanie VecNormalize
                class DummyEnv(gym.Env):
                    def __init__(self):
                        super().__init__()
                        self.observation_space = gym.spaces.Box(-5.0, 5.0, (1451,), dtype=np.float32)
                        self.action_space = gym.spaces.Box(-1.0, 1.0, (4,), dtype=np.float32)
                    
                    def reset(self, **kwargs):
                        return np.zeros(1451, dtype=np.float32), {}
                    
                    def step(self, action):
                        return np.zeros(1451, dtype=np.float32), 0.0, False, False, {}
                
                dummy_env = DummyVecEnv([lambda: DummyEnv()])
                vecnorm = VecNormalize.load(vecnorm_path, dummy_env)
                vecnorm.training = False  # Nastavíme na evaluation mode
                log.info("VecNormalize úspešne načítané")
            except Exception as e:
                log.error(f"Chyba pri načítavaní VecNormalize: {e}")
                vecnorm = None
        
        log.info(f"Používam model: {model_path}")
        
        # ===== PATCH PRE POPLARTSAC LOADING =====
        # Importy pre patch
        import json
        from agent import SimpleCNN1D, SafeReplayBuffer
        from popart_sac import PopArtSAC

        # Načítanie konfigurácie pre feature extraction z config objektu
        feats = config["envSettings"]["feature_columns"]
        training_settings = config["trainingSettings"]
        
        # Nastavenie policy_kwargs s CNN feature extractorom z configu
        policy_kwargs = {
            "net_arch": training_settings["netArch"],
            "features_extractor_class": SimpleCNN1D,
            "features_extractor_kwargs": {
                **training_settings["featureExtractorKwargs"],
                "feature_names": feats,
                "meta_len": 11
            }
        }

        # Aktualizácia custom_objects s proper replay buffer a learning rate
        custom_objects.update({
            "buffer_size": training_settings["bufferSize"],
            "policy_kwargs": policy_kwargs,
            "replay_buffer_class": SafeReplayBuffer,
            "learning_rate": 0.0001
        })
        # ===== KONIEC PATCH =====

        try:
            agent = PopArtSAC.load(model_path, device="cpu", custom_objects=custom_objects)
            log.info(f"Agent typu {type(agent).__name__} načítaný cez PopArtSAC.")
        except Exception as e:
            log.error(f"Chyba pri načítavaní modelu cez PopArtSAC: {e}", exc_info=True)
            exit(1)

        start_dt = datetime.strptime(args.start, "%Y-%m-%d").replace(tzinfo=timezone.utc)
        end_dt = datetime.strptime(args.end, "%Y-%m-%d").replace(hour=23, minute=59, second=59, microsecond=999999, tzinfo=timezone.utc)
        
        # Load data with 1s decision support - check both config and command line
        use_1s_decisions = config.get('use_1s_decisions', False) or args.use_1s_decisions
        backtest_data = load_backtest_data(config, start_dt, end_dt,
                                          load_second_data=True,
                                          use_1s_decisions=use_1s_decisions)

        # --- Skontrolujeme, či máme dosť dát po načítaní ---
        if len(backtest_data['primary']) < config['envSettings']['state_lookback']:
            log.error(f"Nedostatok dát ({len(backtest_data['primary'])} riadkov) pre spustenie backtestu s lookbackom {config['envSettings']['state_lookback']}. Končím.")
            exit(1)


        trades_list, equity_curve_df, final_equity = run_backtest(config, backtest_data, agent, vecnorm,
                                                                  use_1s_decisions=use_1s_decisions)
        trades_df = pd.DataFrame(trades_list)

        log.info("=" * 30 + " VÝSLEDKY BACKTESTU " + "=" * 30)
        initial_equity = config.get('account', {}).get('initialEquity', 10000)
        # Získanie časov z dát, ak krivka neexistuje (napr. žiadne kroky)
        start_time = equity_curve_df.index.min() if not equity_curve_df.empty else (backtest_data['primary'].index.min() if not backtest_data['primary'].empty else start_dt)
        end_time = equity_curve_df.index.max() if not equity_curve_df.empty else (backtest_data['primary'].index.max() if not backtest_data['primary'].empty else end_dt)
        metrics = calculate_metrics(trades_df, equity_curve_df, initial_equity, final_equity, start_time, end_time)

        print("\n" + "=" * 30 + " VÝSLEDKY BACKTESTU " + "=" * 30)
        # Vylepšené formátovanie metrík
        for key, value in metrics.items():
             # Zistíme typ a podľa toho formátujeme
            if isinstance(value, float) and not np.isinf(value) and pd.notna(value):
                # Použijeme .4f pre % a .2f pre $
                if '%' in key or 'Ratio' in key or 'Factor' in key:
                    print(f"{key:<30}: {value:>15.4f}")
                else:
                    print(f"{key:<30}: {value:>15.2f}")
            elif isinstance(value, int):
                print(f"{key:<30}: {value:>15d}")
            else: # Pre stringy ako "N/A" alebo dátumy
                 print(f"{key:<30}: {str(value):>15}")
        print("=" * (60 + len(" VÝSLEDKY BACKTESTU ")) + "\n")

        if args.out_trades:
            try:
                if not trades_df.empty: trades_df.to_csv(args.out_trades, index=False, float_format='%.8f'); log.info(f"Obchody uložené do: {args.out_trades}")
                else: log.info(f"Žiadne obchody, súbor {args.out_trades} nebol vytvorený.")
            except Exception as e: log.error(f"Chyba pri ukladaní obchodov do {args.out_trades}: {e}")
        if args.out_equity:
             try:
                if not equity_curve_df.empty: equity_curve_df.to_csv(args.out_equity, float_format='%.4f'); log.info(f"Equity krivka uložená do: {args.out_equity}")
                else: log.info(f"Equity krivka prázdna, súbor {args.out_equity} nebol vytvorený.")
             except Exception as e: log.error(f"Chyba pri ukladaní equity krivky do {args.out_equity}: {e}")

        # === AUTOMATICKÁ ANALÝZA VÝSLEDKOV ===
        try:
            log.info("🔍 Spúšťam automatickú analýzu výsledkov...")
            from analyze_simulation_results import analyze_simulation_results
            
            # Vytvorenie timestampovaného adresára pre analýzu
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            analysis_dir = f"simulation_analysis_{timestamp}"
            
            analyze_simulation_results(
                trades_file=str(args.out_trades) if args.out_trades else "backtest_trades.csv",
                equity_file=str(args.out_equity) if args.out_equity else "backtest_equity.csv",
                output_dir=analysis_dir
            )
            log.info(f"✅ Analýza dokončená! Výsledky v adresári: {analysis_dir}")
            
        except ImportError:
            log.warning("⚠️ analyze_simulation_results.py nenájdený, preskakujem automatickú analýzu")
        except Exception as e:
            log.error(f"❌ Chyba pri automatickej analýze: {e}")

    except FileNotFoundError as e: log.error(f"Chyba: Súbor nenájdený: {e}"); exit(1)
    except (ValueError, TypeError, KeyError) as e: log.error(f"Chyba v dátach/konfigurácii: {e}", exc_info=True); exit(1)
    except Exception as e: log.error(f"Neočekávaná chyba počas backtestu:", exc_info=True); exit(1)
