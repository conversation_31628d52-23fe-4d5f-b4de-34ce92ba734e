# -*- coding: utf-8 -*-
"""
binance_to_msgpack.py  (v0.3)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
* Fix **KeyError 6** (aggTrades are dicts).
* Adds HTTP 429 back‑off.
* **Completed** file (the previous snippet was truncated at the bottom).

Usage
-----
```bash
python binance_to_msgpack.py \
  --symbol XRPUSDC \
  --date   2025-07-16 \
  --out-dir ~/Projects/scalpel_new/daily_combined_data_binance -v
```

Creates `<out‑dir>/2025‑07‑16.msgpack` with keys:
* date, time_range_iso/ms
* coinapi_symbol (for compatibility)
* trades (from `/aggTrades`)
* ohlcv (all TFs 1 m → 1 w)
* orderbooks (empty list – Binance has no historical depth REST)
"""
from __future__ import annotations

import argparse
import logging
import os
import time
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Tuple

import msgpack  # type: ignore
import requests

BINANCE_BASE = "https://fapi.binance.com/fapi/v1"
TRADE_LIMIT = 1000
KLINE_LIMIT = 1000
BACKOFF_BASE = 1.5  # seconds for rate‑limit backoff

# -----------------------------------------------------------------------------
# Timeframe map: CoinAPI → (Binance, aggregate_n)
# -----------------------------------------------------------------------------
TF_MAP: Dict[str, Tuple[str, int | None]] = {
    "1m": ("1m", None),
    "2m": ("1m", 2),
    "3m": ("3m", None),
    "4m": ("1m", 4),
    "5m": ("5m", None),
    "6m": ("1m", 6),
    "10m": ("1m", 10),
    "15m": ("15m", None),
    "20m": ("1m", 20),
    "30m": ("30m", None),
    "1h": ("1h", None),
    "4h": ("4h", None),
    "1d": ("1d", None),
    "1w": ("1d", 7),
}

# -----------------------------------------------------------------------------
# CLI & logging helpers
# -----------------------------------------------------------------------------

def parse_args() -> argparse.Namespace:
    p = argparse.ArgumentParser(
        description="Build CoinAPI‑style .msgpack for one UTC day using Binance Futures data"
    )
    p.add_argument("--symbol", required=True, help="Binance symbol (e.g. BTCUSDT, XRPUSDC)")
    p.add_argument("--date", required=True, help="UTC date YYYY‑MM‑DD")
    p.add_argument("--out-dir", default="daily_combined_data_binance", help="Destination directory")
    p.add_argument("--min-sleep", type=int, default=200, help="Delay between requests in ms")
    p.add_argument("--verbose", "-v", action="count", default=0, help="Increase log detail: -v, -vv")
    return p.parse_args()


def setup_logging(verbosity: int) -> None:
    level = logging.WARNING if verbosity == 0 else logging.INFO if verbosity == 1 else logging.DEBUG
    logging.basicConfig(
        format="%(asctime)s [%(levelname)s] %(message)s", datefmt="%H:%M:%S", level=level
    )

# -----------------------------------------------------------------------------
# HTTP helper with exponential back‑off
# -----------------------------------------------------------------------------

def _get(url: str, params: Dict[str, Any]) -> Any:
    backoff = BACKOFF_BASE
    while True:
        try:
            r = requests.get(url, params=params, timeout=10)
            if r.status_code == 429:
                logging.warning("HTTP 429 – sleeping %.1f s", backoff)
                time.sleep(backoff)
                backoff = min(backoff * 2, 32)
                continue
            r.raise_for_status()
            return r.json()
        except requests.exceptions.RequestException as e:
            logging.warning("%s – retrying in %.1f s", e, backoff)
            time.sleep(backoff)
            backoff = min(backoff * 2, 32)

# -----------------------------------------------------------------------------
# Fetchers
# -----------------------------------------------------------------------------

def fetch_trades(symbol: str, start_ms: int, end_ms: int, min_sleep_ms: int) -> List[Dict]:
    trades: List[Dict] = []
    params: Dict[str, Any] = {
        "symbol": symbol,
        "startTime": start_ms,
        "endTime": end_ms - 1,
        "limit": TRADE_LIMIT,
    }
    while True:
        batch = _get(f"{BINANCE_BASE}/aggTrades", params)
        if not batch:
            break
        trades.extend(batch)
        last = batch[-1]
        if last["T"] >= end_ms - 1:
            break
        params = {"symbol": symbol, "fromId": last["a"] + 1, "limit": TRADE_LIMIT}
        time.sleep(min_sleep_ms / 1000.0)
    return trades


def fetch_klines(symbol: str, interval: str, start_ms: int, end_ms: int, min_sleep_ms: int) -> List[List]:
    klines: List[List] = []
    cursor = start_ms
    while cursor < end_ms:
        params = {
            "symbol": symbol,
            "interval": interval,
            "startTime": cursor,
            "endTime": min(end_ms - 1, cursor + KLINE_LIMIT * 60_000),
            "limit": KLINE_LIMIT,
        }
        chunk = _get(f"{BINANCE_BASE}/klines", params)
        if not chunk:
            break
        klines.extend(chunk)
        cursor = chunk[-1][0] + 1
        time.sleep(min_sleep_ms / 1000.0)
    return klines

# -----------------------------------------------------------------------------
# Transforms
# -----------------------------------------------------------------------------

def trades_to_coinapi(batch: List[Dict]) -> List[Dict]:
    out: List[Dict] = []
    for t in batch:
        price = float(t["p"])
        size = float(t["q"])
        ts = t["T"]
        is_bm = bool(t["m"])
        taker_side = "SELL" if is_bm else "BUY"
        out.append(
            {
                "uuid": None,
                "price": price,
                "size": size,
                "taker_side": taker_side,
                "is_buyer_maker": is_bm,
                "time_exchange": datetime.utcfromtimestamp(ts / 1000).isoformat() + "Z",
            }
        )
    return out


def klines_to_coinapi(batch: List[List]) -> List[Dict]:
    rows: List[Dict] = []
    for k in batch:
        open_ms, o, h, l, c, v, close_ms = k[0], k[1], k[2], k[3], k[4], k[5], k[6]
        rows.append(
            {
                "time_period_start": datetime.utcfromtimestamp(open_ms / 1000).isoformat() + "Z",
                "time_period_end": datetime.utcfromtimestamp(close_ms / 1000).isoformat() + "Z",
                "price_open": float(o),
                "price_high": float(h),
                "price_low": float(l),
                "price_close": float(c),
                "volume_traded": float(v),
            }
        )
    return rows


def aggregate_n(batch: List[List], n: int) -> List[Dict]:
    if n is None or n <= 1:
        return klines_to_coinapi(batch)
    out: List[Dict] = []
    for i in range(0, len(batch), n):
        seg = batch[i : i + n]
        if len(seg) < n:
            break
        open_ms, close_ms = seg[0][0], seg[-1][6]
        o, c = float(seg[0][1]), float(seg[-1][4])
        h = max(float(x[2]) for x in seg)
        l = min(float(x[3]) for x in seg)
        v = sum(float(x[5]) for x in seg)
        out.append(
            {
                "time_period_start": datetime.utcfromtimestamp(open_ms / 1000).isoformat() + "Z",
                "time_period_end": datetime.utcfromtimestamp(close_ms / 1000).isoformat() + "Z",
                "price_open": o,
                "price_high": h,
                "price_low": l,
                "price_close": c,
                "volume_traded": v,
            }
        )
    return out

# -----------------------------------------------------------------------------
# Utility
# -----------------------------------------------------------------------------

def iso_and_ms(dt: datetime) -> Tuple[str, int]:
    return dt.isoformat(timespec="seconds") + "Z", int(dt.timestamp() * 1000)

# -----------------------------------------------------------------------------
# Main
# -----------------------------------------------------------------------------

def main() -> None:
    args = parse_args()
    setup_logging(args.verbose)

    # Define day boundaries in UTC
    day_start = datetime.strptime(args.date, "%Y-%m-%d").replace(tzinfo=timezone.utc)
    day_end = day_start + timedelta(days=1)
    start_iso, start_ms = iso_and_ms(day_start)
    end_iso, end_ms = iso_and_ms(day_end)

    # Fetch trades
    logging.info("Downloading trades …")
    trades_raw = fetch_trades(args.symbol.upper(), start_ms, end_ms, args.min_sleep)
    logging.info("  %d trades", len(trades_raw))
    trades = trades_to_coinapi(trades_raw)

    # Fetch klines / build ohlcv dict
    ohlcv: Dict[str, List[Dict]] = {}
    for tf, (bin_tf, agg_n) in TF_MAP.items():
        if tf == "1s":
            continue  # Binance REST has no 1‑second klines
        kl_raw = fetch_klines(args.symbol.upper(), bin_tf, start_ms, end_ms, args.min_sleep)
        ohlcv[tf] = aggregate_n(kl_raw, agg_n)
        logging.info("  %s → %d candles", tf, len(ohlcv[tf]))

    # Assemble blob
    blob = {
        "date": args.date,
        "time_range_iso": (start_iso, end_iso),
        "time_range_ms": (start_ms, end_ms),
        "coinapi_symbol": f"BINANCEFTS_PERP_{args.symbol[:-4]}_{args.symbol[-4:]}",
        "orderbooks": [],  # can't fetch historical depth via REST
        "trades": trades,
        "ohlcv": ohlcv,
    }

    # Write
    out_dir = os.path.expanduser(args.out_dir)
    os.makedirs(out_dir, exist_ok=True)
    out_file = os.path.join(out_dir, f"{args.date}.msgpack")
    with open(out_file, "wb") as fh:
        msgpack.pack(blob, fh)
    logging.info("✅ saved %s", out_file)


if __name__ == "__main__":
    main()
