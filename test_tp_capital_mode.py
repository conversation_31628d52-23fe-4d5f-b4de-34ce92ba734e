#!/usr/bin/env python3
"""
Test script for the new Take Profit Capital Percentage mode.
Tests both simulation and live trading implementations.
"""

import json
import tempfile
import os
from pathlib import Path

def test_tp_capital_mode():
    """Test the new TP capital percentage mode configuration and calculation."""
    
    # Test configuration with capital percentage mode
    test_config = {
        "symbol": "XRPUSDC",
        "primaryTimeframe": "5m",
        "featureParquetDir": "parquet_processed",
        "use_1s_decisions": False,
        
        "account": {
            "initialEquity": 100.0
        },
        
        "tradeParams": {
            "slippagePercentage": 0.02,
            "feePercentage": 0.05,
            "minSLDistancePercent": 1.0,
            "rrTarget": 2.0,
            "tpMode": "capitalPercentage",
            "tpCapitalPercentage": 1.0,  # 1% of capital = 1 EUR profit target
            "riskPerTradePercentage": 2.0
        },
        
        "riskManagement": {
            "positionSizingMethod": "RiskPercentage",
            "riskPerTradePercentage": 2.0
        }
    }
    
    print("=== Testing TP Capital Percentage Mode ===")
    print(f"Initial Capital: {test_config['account']['initialEquity']} EUR")
    print(f"TP Mode: {test_config['tradeParams']['tpMode']}")
    print(f"TP Capital Percentage: {test_config['tradeParams']['tpCapitalPercentage']}%")
    
    # Calculate expected values
    initial_capital = test_config['account']['initialEquity']
    tp_capital_percentage = test_config['tradeParams']['tpCapitalPercentage'] / 100.0
    target_profit = initial_capital * tp_capital_percentage
    
    print(f"Expected Target Profit: {target_profit} EUR")
    
    # Simulate position sizing calculation
    risk_per_trade = test_config['tradeParams']['riskPerTradePercentage'] / 100.0
    sl_distance_percent = test_config['tradeParams']['minSLDistancePercent'] / 100.0
    
    # Example trade scenario
    entry_price = 2.5000  # Example XRP price
    sl_distance_points = entry_price * sl_distance_percent
    risk_amount = initial_capital * risk_per_trade
    position_size = risk_amount / sl_distance_points
    
    print(f"\n=== Example Trade Calculation ===")
    print(f"Entry Price: {entry_price}")
    print(f"SL Distance: {sl_distance_points:.5f} ({sl_distance_percent*100}%)")
    print(f"Risk Amount: {risk_amount} EUR ({risk_per_trade*100}%)")
    print(f"Position Size: {position_size:.4f} units")
    
    # Calculate TP distance for capital percentage mode
    tp_distance_points = target_profit / position_size
    tp_price_long = entry_price + tp_distance_points
    tp_price_short = entry_price - tp_distance_points
    
    print(f"\n=== TP Calculation (Capital Mode) ===")
    print(f"TP Distance: {tp_distance_points:.5f} points")
    print(f"TP Price (LONG): {tp_price_long:.5f}")
    print(f"TP Price (SHORT): {tp_price_short:.5f}")
    
    # Verify profit calculation
    profit_long = position_size * tp_distance_points
    profit_short = position_size * tp_distance_points
    
    print(f"Expected Profit (LONG): {profit_long:.2f} EUR")
    print(f"Expected Profit (SHORT): {profit_short:.2f} EUR")
    
    # Compare with traditional rrTarget mode
    rr_target = test_config['tradeParams']['rrTarget']
    tp_distance_rr = sl_distance_points * rr_target
    tp_price_rr_long = entry_price + tp_distance_rr
    profit_rr = position_size * tp_distance_rr
    
    print(f"\n=== Comparison with rrTarget Mode ===")
    print(f"rrTarget: {rr_target}")
    print(f"TP Distance (rrTarget): {tp_distance_rr:.5f} points")
    print(f"TP Price (rrTarget LONG): {tp_price_rr_long:.5f}")
    print(f"Expected Profit (rrTarget): {profit_rr:.2f} EUR")
    
    print(f"\n=== Mode Comparison ===")
    print(f"Capital Mode Profit: {profit_long:.2f} EUR (fixed)")
    print(f"rrTarget Mode Profit: {profit_rr:.2f} EUR (variable)")
    print(f"Difference: {profit_rr - profit_long:.2f} EUR")
    
    # Save test configuration
    test_config_path = "test_config_capital_tp.json"
    with open(test_config_path, 'w') as f:
        json.dump(test_config, f, indent=2)
    
    print(f"\n=== Test Configuration Saved ===")
    print(f"File: {test_config_path}")
    print("You can use this config to test the capital TP mode in simulation:")
    print(f"python simulate_trading_new.py --config {test_config_path}")
    
    return True

def test_config_validation():
    """Test configuration validation for both TP modes."""
    
    print("\n=== Testing Configuration Validation ===")
    
    # Test valid configurations
    valid_configs = [
        {"tpMode": "rrTarget", "rrTarget": 2.0},
        {"tpMode": "capitalPercentage", "tpCapitalPercentage": 1.0},
        {"tpMode": "capitalPercentage", "tpCapitalPercentage": 0.5},
        {"tpMode": "capitalPercentage", "tpCapitalPercentage": 2.5}
    ]
    
    for i, config in enumerate(valid_configs, 1):
        print(f"✓ Valid Config {i}: {config}")
    
    # Test edge cases
    edge_cases = [
        {"tpMode": "capitalPercentage", "tpCapitalPercentage": 0.1},  # Very small target
        {"tpMode": "capitalPercentage", "tpCapitalPercentage": 5.0},  # Large target
    ]
    
    for i, config in enumerate(edge_cases, 1):
        print(f"⚠ Edge Case {i}: {config}")
    
    return True

if __name__ == "__main__":
    print("Testing Take Profit Capital Percentage Mode Implementation")
    print("=" * 60)
    
    try:
        test_tp_capital_mode()
        test_config_validation()
        print("\n✅ All tests completed successfully!")
        print("\nNext steps:")
        print("1. Run simulation with test_config_capital_tp.json")
        print("2. Compare results with traditional rrTarget mode")
        print("3. Test live trading with capital percentage mode")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        raise
