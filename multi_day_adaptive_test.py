#!/usr/bin/env python3
"""
Multi-day adaptive parameter testing

Tests the optimized adaptive configuration across multiple days
to validate consistency and performance.
"""

import json
import subprocess
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)

def run_multi_day_test():
    """Run adaptive configuration test across multiple days"""
    
    # Test dates (July 2025 data)
    test_dates = [
        "2025-07-01",
        "2025-07-02", 
        "2025-07-03",
        "2025-07-04",
        "2025-07-05"
    ]
    
    model_path = "sac_3198976_steps.zip"
    config_path = "strategyConfig_scalp_1s.json"
    
    results = []
    
    log.info(f"🚀 Starting multi-day adaptive test for {len(test_dates)} days")
    
    for i, date in enumerate(test_dates):
        log.info(f"📅 Testing day {i+1}/{len(test_dates)}: {date}")
        
        try:
            # Run simulation
            cmd = [
                'python', 'simulate_trading_new.py',
                '--cfg', config_path,
                '--start', date,
                '--end', date,
                '--model', model_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                # Parse results from output
                output_lines = result.stdout.split('\n')
                
                metrics = {
                    'date': date,
                    'success': True,
                    'total_pnl': 0.0,
                    'total_trades': 0,
                    'win_rate': 0.0,
                    'profit_factor': 0.0,
                    'max_drawdown': 0.0,
                    'threshold_breaches': 0,
                    'long_signals': 0,
                    'short_signals': 0
                }
                
                # Parse metrics from output
                for line in output_lines:
                    if 'Celkový PnL ($)' in line:
                        try:
                            metrics['total_pnl'] = float(line.split(':')[1].strip())
                        except:
                            pass
                    elif 'Počet obchodov' in line:
                        try:
                            metrics['total_trades'] = int(line.split(':')[1].strip())
                        except:
                            pass
                    elif 'Win Rate (%)' in line:
                        try:
                            metrics['win_rate'] = float(line.split(':')[1].strip())
                        except:
                            pass
                    elif 'Profit Factor' in line:
                        try:
                            metrics['profit_factor'] = float(line.split(':')[1].strip())
                        except:
                            pass
                    elif 'Maximálny Drawdown (%)' in line:
                        try:
                            metrics['max_drawdown'] = float(line.split(':')[1].strip())
                        except:
                            pass
                    elif 'Total threshold breaches:' in line:
                        try:
                            metrics['threshold_breaches'] = int(line.split(':')[1].strip())
                        except:
                            pass
                    elif 'Signals above long threshold' in line:
                        try:
                            metrics['long_signals'] = int(line.split(':')[1].strip())
                        except:
                            pass
                    elif 'Signals below short threshold' in line:
                        try:
                            metrics['short_signals'] = int(line.split(':')[1].strip())
                        except:
                            pass
                
                results.append(metrics)
                log.info(f"✅ {date}: PnL=${metrics['total_pnl']:.3f}, Trades={metrics['total_trades']}, Breaches={metrics['threshold_breaches']}")
                
            else:
                log.error(f"❌ {date}: Simulation failed - {result.stderr}")
                results.append({
                    'date': date,
                    'success': False,
                    'error': result.stderr
                })
        
        except subprocess.TimeoutExpired:
            log.error(f"❌ {date}: Simulation timed out")
            results.append({
                'date': date,
                'success': False,
                'error': 'Timeout'
            })
        
        except Exception as e:
            log.error(f"❌ {date}: Error - {e}")
            results.append({
                'date': date,
                'success': False,
                'error': str(e)
            })
    
    # Save results
    df_results = pd.DataFrame(results)
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_file = f'multi_day_adaptive_test_{timestamp}.csv'
    df_results.to_csv(results_file, index=False)
    
    log.info(f"📊 Results saved to: {results_file}")
    
    # Analyze results
    analyze_multi_day_results(df_results)
    
    return df_results

def analyze_multi_day_results(df_results):
    """Analyze multi-day test results"""
    
    log.info("🔍 Analyzing multi-day adaptive test results...")
    
    # Filter successful runs
    successful_runs = df_results[df_results['success'] == True].copy()
    
    if len(successful_runs) == 0:
        log.error("No successful runs found!")
        return
    
    log.info(f"Successful runs: {len(successful_runs)}/{len(df_results)}")
    
    # Overall statistics
    total_pnl = successful_runs['total_pnl'].sum()
    total_trades = successful_runs['total_trades'].sum()
    avg_win_rate = successful_runs['win_rate'].mean()
    avg_profit_factor = successful_runs['profit_factor'].mean()
    total_breaches = successful_runs['threshold_breaches'].sum()
    total_long_signals = successful_runs['long_signals'].sum()
    total_short_signals = successful_runs['short_signals'].sum()
    
    log.info("\n" + "="*60)
    log.info("📊 MULTI-DAY ADAPTIVE TEST SUMMARY")
    log.info("="*60)
    log.info(f"Test period: {successful_runs['date'].min()} to {successful_runs['date'].max()}")
    log.info(f"Successful days: {len(successful_runs)}")
    log.info(f"Total PnL: ${total_pnl:.3f}")
    log.info(f"Average daily PnL: ${total_pnl/len(successful_runs):.3f}")
    log.info(f"Total trades: {total_trades}")
    log.info(f"Average trades per day: {total_trades/len(successful_runs):.1f}")
    log.info(f"Average win rate: {avg_win_rate:.1f}%")
    log.info(f"Average profit factor: {avg_profit_factor:.2f}")
    log.info(f"Total threshold breaches: {total_breaches}")
    log.info(f"Total LONG signals: {total_long_signals}")
    log.info(f"Total SHORT signals: {total_short_signals}")
    
    # Daily breakdown
    log.info("\n📅 DAILY BREAKDOWN:")
    log.info("-" * 80)
    log.info(f"{'Date':<12} {'PnL ($)':<10} {'Trades':<8} {'Win%':<8} {'PF':<8} {'Breaches':<10}")
    log.info("-" * 80)
    
    for _, row in successful_runs.iterrows():
        log.info(f"{row['date']:<12} {row['total_pnl']:<10.3f} {row['total_trades']:<8} "
                f"{row['win_rate']:<8.1f} {row['profit_factor']:<8.2f} {row['threshold_breaches']:<10}")
    
    # Performance consistency
    pnl_std = successful_runs['total_pnl'].std()
    trades_std = successful_runs['total_trades'].std()
    
    log.info(f"\n📈 CONSISTENCY METRICS:")
    log.info(f"PnL standard deviation: ${pnl_std:.3f}")
    log.info(f"Trades standard deviation: {trades_std:.1f}")
    log.info(f"Profitable days: {len(successful_runs[successful_runs['total_pnl'] > 0])}/{len(successful_runs)} ({len(successful_runs[successful_runs['total_pnl'] > 0])/len(successful_runs)*100:.1f}%)")
    
    # Best and worst days
    best_day = successful_runs.loc[successful_runs['total_pnl'].idxmax()]
    worst_day = successful_runs.loc[successful_runs['total_pnl'].idxmin()]
    
    log.info(f"\n🏆 BEST DAY: {best_day['date']} - PnL: ${best_day['total_pnl']:.3f}, Trades: {best_day['total_trades']}")
    log.info(f"💔 WORST DAY: {worst_day['date']} - PnL: ${worst_day['total_pnl']:.3f}, Trades: {worst_day['total_trades']}")
    
    # Adaptive thresholds effectiveness
    breach_rate = total_breaches / len(successful_runs)
    signal_conversion_rate = total_trades / total_breaches if total_breaches > 0 else 0
    
    log.info(f"\n🎯 ADAPTIVE THRESHOLDS EFFECTIVENESS:")
    log.info(f"Average threshold breaches per day: {breach_rate:.1f}")
    log.info(f"Signal to trade conversion rate: {signal_conversion_rate:.1f}")
    log.info(f"LONG vs SHORT signal ratio: {total_long_signals}:{total_short_signals}")
    
    # Recommendations
    log.info(f"\n💡 RECOMMENDATIONS:")
    if avg_profit_factor > 2.0:
        log.info("✅ Excellent profit factor - adaptive thresholds working well")
    elif avg_profit_factor > 1.5:
        log.info("⚠️  Good profit factor - consider fine-tuning threshold sensitivity")
    else:
        log.info("❌ Low profit factor - review threshold configuration")
    
    if breach_rate < 1.0:
        log.info("⚠️  Low threshold breach rate - consider lowering base thresholds")
    elif breach_rate > 5.0:
        log.info("⚠️  High threshold breach rate - consider raising base thresholds")
    else:
        log.info("✅ Good threshold breach rate")
    
    if pnl_std / abs(total_pnl/len(successful_runs)) < 0.5:
        log.info("✅ Consistent daily performance")
    else:
        log.info("⚠️  High performance variability - consider additional filters")

def main():
    """Main function"""
    
    log.info("🚀 Starting multi-day adaptive parameter test...")
    
    try:
        results = run_multi_day_test()
        log.info("✅ Multi-day test completed successfully!")
        return results
        
    except Exception as e:
        log.error(f"❌ Multi-day test failed: {e}", exc_info=True)
        return None

if __name__ == "__main__":
    results = main()
