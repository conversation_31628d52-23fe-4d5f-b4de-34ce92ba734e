#!/usr/bin/env python3
"""
Enhanced trading simulation with improved entry signals and trend reversal detection
Based on analysis of current results showing 25% win rate and profit factor 0.41
"""
import os, sys, json, logging, argparse
from pathlib import Path
from datetime import timedelta
import pandas as pd
import numpy as np
import torch
import gymnasium as gym

# Import our modules
from indicators import calculate_and_merge_indicators
from popart_sac import PopArtSAC
from agent import SimpleCNN1D, SafeReplayBuffer
from stable_baselines3.common.vec_env import VecNormalize, DummyVecEnv

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(name)s] [%(levelname)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
    handlers=[logging.StreamHandler(sys.stdout)]
)
log = logging.getLogger("EnhancedTrading")

def load_config(path: Path):
    """Load configuration from JSON file"""
    with open(path, 'r') as f:
        content = f.read()
        # Replace environment variables
        import re
        def replace_env_var(match):
            var_name = match.group(1)
            return os.environ.get(var_name, match.group(0))
        content = re.sub(r'\$\{([^}]+)\}', replace_env_var, content)
        cfg = json.loads(content)
    return cfg

class EnhancedTradingEngine:
    """Enhanced trading engine with improved signal processing"""
    
    def __init__(self, cfg):
        self.cfg = cfg
        self.pos = 0  # Current position: 0=none, 1=long, -1=short
        self.size = 0.0
        self.entry_price = 0.0
        self.balance = cfg["account"]["initialEquity"]
        self.trades = []
        
        # Enhanced signal analysis
        self.signal_history = []
        self.price_history = []
        self.max_history = 20
        
        # Dynamic thresholds based on market conditions
        self.base_entry_threshold = 0.5  # Lower than original 0.7
        self.base_reversal_threshold = 0.8  # Higher for reversals
        
        # Time-based filters (avoid worst performing hours)
        self.avoid_hours = [0, 16, 17]  # Based on analysis
        self.preferred_hours = [15]  # Best performing hour
        
        # Trend detection parameters
        self.trend_lookback = 10
        self.momentum_threshold = 0.3
        
    def is_good_trading_time(self, timestamp):
        """Check if current time is good for trading"""
        hour = timestamp.hour
        
        # Avoid worst performing hours
        if hour in self.avoid_hours:
            return False, f"avoiding_hour_{hour}"
        
        # Prefer best performing hours
        if hour in self.preferred_hours:
            return True, f"preferred_hour_{hour}"
        
        return True, "normal_hours"
    
    def analyze_market_conditions(self, entry_sig, current_price):
        """Analyze current market conditions for dynamic thresholds"""
        
        # Update history
        self.signal_history.append(entry_sig)
        self.price_history.append(current_price)
        
        if len(self.signal_history) > self.max_history:
            self.signal_history.pop(0)
            self.price_history.pop(0)
        
        if len(self.signal_history) < 5:
            return {
                'volatility': 0.5,
                'trend_strength': 0.0,
                'momentum': 0.0,
                'signal_consistency': 0.0
            }
        
        # Calculate market metrics
        recent_signals = np.array(self.signal_history[-10:])
        recent_prices = np.array(self.price_history[-10:])
        
        # Volatility (signal variance)
        volatility = np.std(recent_signals)
        
        # Trend strength (directional consistency)
        trend_strength = abs(np.mean(recent_signals))
        
        # Momentum (recent acceleration)
        if len(recent_signals) >= 3:
            momentum = abs(recent_signals[-1] - recent_signals[-3])
        else:
            momentum = 0.0
        
        # Signal consistency (how stable signals are)
        signal_consistency = 1.0 / (1.0 + volatility)  # Higher consistency = lower volatility
        
        return {
            'volatility': volatility,
            'trend_strength': trend_strength,
            'momentum': momentum,
            'signal_consistency': signal_consistency
        }
    
    def get_dynamic_thresholds(self, market_conditions, timestamp):
        """Calculate dynamic thresholds based on market conditions and time"""
        
        # Base thresholds
        entry_threshold = self.base_entry_threshold
        reversal_threshold = self.base_reversal_threshold
        
        # Time-based adjustments
        hour = timestamp.hour
        if hour in self.preferred_hours:
            # More aggressive during best hours
            entry_threshold *= 0.8  # Lower threshold = easier entry
        elif hour in self.avoid_hours:
            # More conservative during worst hours
            entry_threshold *= 1.3  # Higher threshold = harder entry
        
        # Market condition adjustments
        volatility = market_conditions['volatility']
        trend_strength = market_conditions['trend_strength']
        momentum = market_conditions['momentum']
        consistency = market_conditions['signal_consistency']
        
        # High volatility = require stronger signals
        if volatility > 0.6:
            entry_threshold *= 1.2
            reversal_threshold *= 1.1
        elif volatility < 0.3:
            # Low volatility = can be more aggressive
            entry_threshold *= 0.9
        
        # Strong trend = easier reversals
        if trend_strength > 0.6:
            reversal_threshold *= 0.9
        
        # High momentum = easier entries
        if momentum > self.momentum_threshold:
            entry_threshold *= 0.9
        
        # High consistency = can be more aggressive
        if consistency > 0.7:
            entry_threshold *= 0.95
        
        # Ensure reasonable bounds
        entry_threshold = max(0.3, min(0.9, entry_threshold))
        reversal_threshold = max(0.6, min(1.0, reversal_threshold))
        
        return {
            'entry': entry_threshold,
            'reversal': reversal_threshold
        }
    
    def detect_trend_reversal(self, entry_sig, market_conditions):
        """Enhanced trend reversal detection"""
        
        if len(self.signal_history) < 5:
            return False, "insufficient_history"
        
        recent_signals = np.array(self.signal_history[-5:])
        
        # Check for signal direction change
        current_direction = np.sign(entry_sig)
        prev_direction = np.sign(np.mean(recent_signals[:-1]))
        
        # Strong reversal conditions
        reversal_strength = abs(entry_sig)
        momentum = market_conditions['momentum']
        
        # Reversal detected if:
        # 1. Direction changed
        # 2. Current signal is strong enough
        # 3. There's sufficient momentum
        if (current_direction != prev_direction and 
            reversal_strength > 0.6 and 
            momentum > 0.2):
            return True, f"reversal_detected_strength_{reversal_strength:.3f}"
        
        return False, "no_reversal"
    
    def execute_decision(self, action, current_price, current_time):
        """Execute trading decision with enhanced logic"""
        entry_sig = action[0]
        exit_sig = action[3] if len(action) > 3 else 0.0
        
        # Check trading time
        time_ok, time_reason = self.is_good_trading_time(current_time)
        
        # Analyze market conditions
        market_conditions = self.analyze_market_conditions(entry_sig, current_price)
        
        # Get dynamic thresholds
        thresholds = self.get_dynamic_thresholds(market_conditions, current_time)
        
        # Log detailed analysis every 1000 steps
        if len(self.signal_history) % 1000 == 0:
            log.info(f"🔍 MARKET ANALYSIS: sig={entry_sig:.3f}, vol={market_conditions['volatility']:.3f}, "
                    f"trend={market_conditions['trend_strength']:.3f}, mom={market_conditions['momentum']:.3f}")
            log.info(f"📊 THRESHOLDS: entry={thresholds['entry']:.3f}, reversal={thresholds['reversal']:.3f}, "
                    f"time={time_reason}")
        
        # Entry logic
        if self.pos == 0:
            if not time_ok:
                return  # Skip trading during bad hours
            
            triggered_pos = 0
            
            if entry_sig > thresholds['entry']:
                triggered_pos = 1
                log.info(f"🟢 LONG ENTRY: sig={entry_sig:.3f} > thr={thresholds['entry']:.3f} ({time_reason})")
            elif entry_sig < -thresholds['entry']:
                triggered_pos = -1
                log.info(f"🔴 SHORT ENTRY: sig={entry_sig:.3f} < thr={-thresholds['entry']:.3f} ({time_reason})")

            if triggered_pos != 0:
                self._open_position(triggered_pos, current_price, current_time, entry_sig)
        
        # Exit logic with enhanced trend reversal
        elif self.pos != 0:
            exit_triggered = False
            exit_reason = ""
            exit_price = current_price
            new_position = 0
            
            # Enhanced trend reversal detection
            reversal_detected, reversal_reason = self.detect_trend_reversal(entry_sig, market_conditions)
            
            if reversal_detected:
                # Strong reversal signal
                if self.pos == 1 and entry_sig < -thresholds['reversal']:
                    exit_triggered = True
                    exit_reason = f"TREND_REVERSAL_TO_SHORT_{reversal_reason}"
                    new_position = -1
                    log.info(f"🔄 TREND REVERSAL: LONG→SHORT, sig={entry_sig:.3f}, reason={reversal_reason}")
                    
                elif self.pos == -1 and entry_sig > thresholds['reversal']:
                    exit_triggered = True
                    exit_reason = f"TREND_REVERSAL_TO_LONG_{reversal_reason}"
                    new_position = 1
                    log.info(f"🔄 TREND REVERSAL: SHORT→LONG, sig={entry_sig:.3f}, reason={reversal_reason}")
            
            # Enhanced SL/TP logic with dynamic levels
            if not exit_triggered:
                # Dynamic SL/TP based on market volatility
                volatility_multiplier = 1.0 + market_conditions['volatility']
                
                if self.pos == 1:  # Long position
                    sl_level = self.entry_price * (1.0 - 0.015 * volatility_multiplier)  # Dynamic SL
                    tp_level = self.entry_price * (1.0 + 0.025 * volatility_multiplier)  # Dynamic TP
                    
                    if current_price <= sl_level:
                        exit_triggered = True
                        exit_reason = f"SL_dynamic_{volatility_multiplier:.2f}"
                        exit_price = sl_level
                    elif current_price >= tp_level:
                        exit_triggered = True
                        exit_reason = f"TP_dynamic_{volatility_multiplier:.2f}"
                        exit_price = tp_level
                        
                elif self.pos == -1:  # Short position
                    sl_level = self.entry_price * (1.0 + 0.015 * volatility_multiplier)  # Dynamic SL
                    tp_level = self.entry_price * (1.0 - 0.025 * volatility_multiplier)  # Dynamic TP
                    
                    if current_price >= sl_level:
                        exit_triggered = True
                        exit_reason = f"SL_dynamic_{volatility_multiplier:.2f}"
                        exit_price = sl_level
                    elif current_price <= tp_level:
                        exit_triggered = True
                        exit_reason = f"TP_dynamic_{volatility_multiplier:.2f}"
                        exit_price = tp_level
            
            if exit_triggered:
                self._close_position(exit_price, current_time, exit_reason)
                
                # Open new position if reversal (only during good trading hours)
                if new_position != 0 and time_ok:
                    self._open_position(new_position, current_price, current_time, entry_sig)
    
    def _open_position(self, direction, price, timestamp, signal):
        """Open a new position"""
        # Calculate position size
        risk_per_trade = self.cfg.get("tradeParams", {}).get("riskPerTrade", 0.015)  # Reduced risk
        risk_amount = self.balance * risk_per_trade
        
        # Dynamic SL distance based on volatility
        market_conditions = self.analyze_market_conditions(signal, price)
        volatility_multiplier = 1.0 + market_conditions['volatility']
        min_sl_dist_perc = 0.015 * volatility_multiplier  # Dynamic SL distance
        
        sl_distance = price * min_sl_dist_perc
        
        self.size = risk_amount / sl_distance if sl_distance > 0 else 0
        self.pos = direction
        self.entry_price = price
        
        direction_str = "LONG" if direction == 1 else "SHORT"
        log.info(f"📈 {direction_str} ENTRY @ {price:.4f}, size: {self.size:.2f}, "
                f"signal: {signal:.4f}, vol_mult: {volatility_multiplier:.2f}")
    
    def _close_position(self, exit_price, timestamp, reason):
        """Close current position"""
        if self.pos == 0:
            return
            
        # Calculate PnL
        pnl = (exit_price - self.entry_price) * self.size * self.pos
        self.balance += pnl
        
        # Record trade
        trade = {
            'entry_time': timestamp,
            'exit_time': timestamp,
            'direction': "Long" if self.pos == 1 else "Short",
            'size': self.size,
            'entry_price': self.entry_price,
            'exit_price': exit_price,
            'pnl': pnl,
            'exit_reason': reason
        }
        self.trades.append(trade)
        
        direction_str = "LONG" if self.pos == 1 else "SHORT"
        log.info(f"📉 {direction_str} EXIT @ {exit_price:.4f}, PnL: ${pnl:.2f}, Reason: {reason}")
        
        # Reset position
        self.pos = 0
        self.size = 0.0
        self.entry_price = 0.0

def load_historical_data(symbol: str, start_date: str, end_date: str, timeframe: str = "1s"):
    """Load historical data from parquet files"""
    log.info(f"Loading historical data for {symbol} from {start_date} to {end_date}")
    
    # Convert dates
    start_dt = pd.to_datetime(start_date).tz_localize('UTC')
    end_dt = pd.to_datetime(end_date).tz_localize('UTC') + pd.Timedelta(days=1) - pd.Timedelta(seconds=1)
    
    # Load data from parquet files
    data_dir = Path("parquet_processed") / symbol / timeframe
    
    dfs = []
    current_date = start_dt.date()
    end_date_obj = end_dt.date()
    
    while current_date <= end_date_obj:
        file_path = data_dir / f"{current_date}.parquet"
        if file_path.exists():
            df = pd.read_parquet(file_path)
            df.index = pd.to_datetime(df.index, utc=True)
            dfs.append(df)
            log.info(f"Loaded {len(df)} rows from {file_path}")
        else:
            log.warning(f"File not found: {file_path}")
        
        current_date += timedelta(days=1)
    
    if not dfs:
        raise ValueError(f"No data files found for {symbol} in range {start_date} to {end_date}")
    
    # Combine all data
    data = pd.concat(dfs, axis=0)
    data = data.sort_index()
    
    # Filter to exact time range
    data = data[(data.index >= start_dt) & (data.index <= end_dt)]
    
    log.info(f"Total loaded: {len(data)} rows from {start_dt} to {end_dt}")
    return data

def main():
    """Main simulation function"""
    parser = argparse.ArgumentParser(description="Enhanced Trading Simulation")
    parser.add_argument("--cfg", required=True, help="Configuration file path")
    parser.add_argument("--start", required=True, help="Start date (YYYY-MM-DD)")
    parser.add_argument("--end", required=True, help="End date (YYYY-MM-DD)")
    parser.add_argument("--out-trades", help="Output trades CSV file")
    parser.add_argument("--out-equity", help="Output equity CSV file")
    
    args = parser.parse_args()
    
    # Load configuration
    cfg = load_config(Path(args.cfg))
    symbol = cfg["symbol"]
    
    log.info(f"🚀 Starting enhanced simulation for {symbol}")
    log.info(f"📅 Period: {args.start} to {args.end}")
    
    # Load historical data
    data = load_historical_data(symbol, args.start, args.end)
    
    # Calculate indicators
    log.info("📊 Calculating indicators...")
    data_with_indicators = calculate_and_merge_indicators(data, cfg)
    
    # Load model and VecNormalize
    model_path = cfg["model"]["path"]
    vecnorm_path = cfg["model"]["vecnormalize_path"]
    log.info(f"🤖 Loading model: {model_path}")
    
    try:
        vec_normalize = VecNormalize.load(vecnorm_path)
        vec_normalize.training = False
        model = PopArtSAC.load(model_path)
        log.info("✅ Model and VecNormalize loaded successfully")
    except Exception as e:
        log.error(f"❌ Failed to load model: {e}")
        return
    
    # Initialize enhanced trading engine
    te = EnhancedTradingEngine(cfg)
    
    # Prepare features
    feature_columns = cfg.get("features", [])
    log.info(f"📋 Using {len(feature_columns)} features")
    
    # Run simulation
    log.info("🎯 Starting enhanced trading simulation...")
    
    equity_history = []
    lookback = 30
    
    for i, (timestamp, row) in enumerate(data_with_indicators.iterrows()):
        if i < lookback:
            continue
            
        current_price = row['close']
        
        # Prepare state for model
        try:
            start_idx = max(0, i - lookback + 1)
            end_idx = i + 1
            window_data = data_with_indicators.iloc[start_idx:end_idx]
            
            # Extract features
            feature_data = []
            for col in feature_columns:
                if col in window_data.columns:
                    feature_data.append(window_data[col].values)
                else:
                    feature_data.append(np.zeros(len(window_data)))
            
            state = np.column_stack(feature_data).flatten().astype(np.float32)
            state_input = state.reshape(1, -1)
            
            # Apply VecNormalize and get prediction
            try:
                state_normalized = vec_normalize.normalize_obs(state_input)
                action, _ = model.predict(state_normalized[0], deterministic=True)
            except Exception as e:
                log.warning(f"Model prediction error: {e}")
                action = [0.0, 0.0, 0.0, 0.0]
            
        except Exception as e:
            log.warning(f"State preparation error: {e}")
            action = [0.0, 0.0, 0.0, 0.0]
        
        # Execute enhanced trading decision
        te.execute_decision(action, current_price, timestamp)
        
        # Track equity
        unrealized_pnl = 0.0
        if te.pos != 0 and te.entry_price > 0:
            unrealized_pnl = (current_price - te.entry_price) * te.size * te.pos
        
        total_equity = te.balance + unrealized_pnl
        equity_history.append({
            'timestamp': timestamp,
            'equity': total_equity,
            'realized_pnl': te.balance - cfg["account"]["initialEquity"],
            'unrealized_pnl': unrealized_pnl,
            'position': te.pos
        })
        
        # Progress logging
        if i % 5000 == 0:
            log.info(f"📈 Step {i}/{len(data_with_indicators)}: Equity=${total_equity:.2f}, Pos={te.pos}")
    
    # Final results
    final_equity = te.balance
    total_pnl = final_equity - cfg["account"]["initialEquity"]
    
    log.info(f"🏁 ENHANCED SIMULATION COMPLETE")
    log.info(f"💰 Final Equity: ${final_equity:.2f}")
    log.info(f"📊 Total PnL: ${total_pnl:.2f} ({total_pnl/cfg['account']['initialEquity']*100:.2f}%)")
    log.info(f"🔢 Total Trades: {len(te.trades)}")
    
    if len(te.trades) > 0:
        winning_trades = len([t for t in te.trades if t['pnl'] > 0])
        win_rate = winning_trades / len(te.trades)
        log.info(f"🎯 Win Rate: {win_rate:.1%}")
    
    # Save results
    if args.out_trades and te.trades:
        trades_df = pd.DataFrame(te.trades)
        trades_df.to_csv(args.out_trades, index=False)
        log.info(f"💾 Trades saved to: {args.out_trades}")
    
    if args.out_equity:
        equity_df = pd.DataFrame(equity_history)
        equity_df.to_csv(args.out_equity, index=False)
        log.info(f"💾 Equity history saved to: {args.out_equity}")

if __name__ == "__main__":
    main()
