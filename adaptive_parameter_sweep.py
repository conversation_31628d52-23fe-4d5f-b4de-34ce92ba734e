#!/usr/bin/env python3
"""
Parameter sweep for adaptive signal smoothing and threshold tuning

This script tests different combinations of adaptive parameters to find
optimal settings for the trading system.
"""

import json
import subprocess
import pandas as pd
import numpy as np
from itertools import product
import logging
from pathlib import Path
import time
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)

def create_adaptive_config_variants():
    """Create different configuration variants for testing"""
    
    # Base configuration
    base_config_path = "strategyConfig_scalp_1s.json"
    with open(base_config_path, 'r') as f:
        base_config = json.load(f)
    
    # Parameter combinations to test
    smoothing_methods = ['ema', 'kama', 'median_ema', 'kalman', 'trend_confirm']
    confidence_weights = [0.3, 0.5, 0.7]
    volatility_weights = [0.1, 0.2, 0.3]
    base_thresholds = [0.6, 0.8, 0.9]
    
    # KAMA specific parameters
    kama_fast_sc = [0.5, 0.666, 0.8]
    kama_slow_sc = [0.04, 0.064, 0.1]
    
    # Median EMA parameters
    median_windows = [3, 5, 7]
    
    # Trend confirmation parameters
    trend_ema_short = [3, 5, 8]
    trend_ema_long = [8, 12, 21]
    
    configs = []
    config_id = 0
    
    for method in smoothing_methods:
        for conf_weight in confidence_weights:
            for vol_weight in volatility_weights:
                for base_thr in base_thresholds:
                    
                    # Create base configuration
                    config = base_config.copy()
                    config['tradeParams']['longEntryThreshold'] = base_thr
                    config['tradeParams']['shortEntryThreshold'] = base_thr
                    config['tradeParams']['exitActionThreshold'] = base_thr
                    
                    config['tradeParams']['signalSmoothing']['method'] = method
                    config['tradeParams']['thresholdTuning']['confidenceWeight'] = conf_weight
                    config['tradeParams']['thresholdTuning']['volatilityWeight'] = vol_weight
                    
                    if method == 'kama':
                        # Test different KAMA parameters
                        for fast_sc in kama_fast_sc:
                            for slow_sc in kama_slow_sc:
                                kama_config = config.copy()
                                kama_config['tradeParams']['signalSmoothing']['kamaFastSC'] = fast_sc
                                kama_config['tradeParams']['signalSmoothing']['kamaSlowSC'] = slow_sc
                                
                                configs.append({
                                    'id': config_id,
                                    'config': kama_config,
                                    'description': f"{method}_conf{conf_weight}_vol{vol_weight}_thr{base_thr}_fast{fast_sc}_slow{slow_sc}"
                                })
                                config_id += 1
                    
                    elif method == 'median_ema':
                        # Test different median window sizes
                        for window in median_windows:
                            median_config = config.copy()
                            median_config['tradeParams']['signalSmoothing']['medianWindow'] = window
                            
                            configs.append({
                                'id': config_id,
                                'config': median_config,
                                'description': f"{method}_conf{conf_weight}_vol{vol_weight}_thr{base_thr}_win{window}"
                            })
                            config_id += 1
                    
                    elif method == 'trend_confirm':
                        # Test different trend EMA periods
                        for short_ema in trend_ema_short:
                            for long_ema in trend_ema_long:
                                if short_ema < long_ema:  # Ensure short < long
                                    trend_config = config.copy()
                                    trend_config['tradeParams']['signalSmoothing']['trendEmaShort'] = short_ema
                                    trend_config['tradeParams']['signalSmoothing']['trendEmaLong'] = long_ema
                                    
                                    configs.append({
                                        'id': config_id,
                                        'config': trend_config,
                                        'description': f"{method}_conf{conf_weight}_vol{vol_weight}_thr{base_thr}_short{short_ema}_long{long_ema}"
                                    })
                                    config_id += 1
                    
                    else:
                        # Standard methods (ema, kalman)
                        configs.append({
                            'id': config_id,
                            'config': config,
                            'description': f"{method}_conf{conf_weight}_vol{vol_weight}_thr{base_thr}"
                        })
                        config_id += 1
    
    log.info(f"Generated {len(configs)} configuration variants")
    return configs

def run_simulation(config_data, config_id, start_date, end_date, model_path):
    """Run a single simulation with given configuration"""
    
    # Save temporary config file
    temp_config_path = f"temp_adaptive_config_{config_id}.json"
    with open(temp_config_path, 'w') as f:
        json.dump(config_data['config'], f, indent=2)
    
    try:
        # Run simulation
        cmd = [
            'python', 'simulate_trading_new.py',
            '--cfg', temp_config_path,
            '--start', start_date,
            '--end', end_date,
            '--model', model_path
        ]
        
        log.info(f"Running simulation {config_id}: {config_data['description']}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            # Parse results from output
            output_lines = result.stdout.split('\n')
            
            # Extract key metrics
            metrics = {
                'config_id': config_id,
                'description': config_data['description'],
                'success': True,
                'total_pnl': 0.0,
                'total_trades': 0,
                'win_rate': 0.0,
                'profit_factor': 0.0,
                'max_drawdown': 0.0,
                'avg_trade_duration': 0.0
            }
            
            # Parse metrics from output
            for line in output_lines:
                if 'Celkový PnL ($)' in line:
                    try:
                        metrics['total_pnl'] = float(line.split(':')[1].strip())
                    except:
                        pass
                elif 'Počet obchodov' in line:
                    try:
                        metrics['total_trades'] = int(line.split(':')[1].strip())
                    except:
                        pass
                elif 'Win Rate (%)' in line:
                    try:
                        metrics['win_rate'] = float(line.split(':')[1].strip())
                    except:
                        pass
                elif 'Profit Factor' in line:
                    try:
                        metrics['profit_factor'] = float(line.split(':')[1].strip())
                    except:
                        pass
                elif 'Maximálny Drawdown (%)' in line:
                    try:
                        metrics['max_drawdown'] = float(line.split(':')[1].strip())
                    except:
                        pass
                elif 'Priemerná dĺžka obchodu:' in line:
                    try:
                        duration_str = line.split(':')[1].strip().replace(' minút', '')
                        metrics['avg_trade_duration'] = float(duration_str)
                    except:
                        pass
            
            return metrics
            
        else:
            log.error(f"Simulation {config_id} failed: {result.stderr}")
            return {
                'config_id': config_id,
                'description': config_data['description'],
                'success': False,
                'error': result.stderr
            }
    
    except subprocess.TimeoutExpired:
        log.error(f"Simulation {config_id} timed out")
        return {
            'config_id': config_id,
            'description': config_data['description'],
            'success': False,
            'error': 'Timeout'
        }
    
    except Exception as e:
        log.error(f"Simulation {config_id} error: {e}")
        return {
            'config_id': config_id,
            'description': config_data['description'],
            'success': False,
            'error': str(e)
        }
    
    finally:
        # Clean up temporary config file
        try:
            Path(temp_config_path).unlink()
        except:
            pass

def run_parameter_sweep():
    """Run the complete parameter sweep"""
    
    # Configuration
    start_date = "2025-07-01"
    end_date = "2025-07-01"  # Single day for fast testing
    model_path = "sac_3198976_steps.zip"
    
    # Generate configurations
    configs = create_adaptive_config_variants()
    
    # Limit to reasonable number for testing
    max_configs = 50
    if len(configs) > max_configs:
        log.info(f"Limiting to first {max_configs} configurations for testing")
        configs = configs[:max_configs]
    
    # Run simulations
    results = []
    start_time = time.time()
    
    for i, config_data in enumerate(configs):
        log.info(f"Progress: {i+1}/{len(configs)} ({(i+1)/len(configs)*100:.1f}%)")
        
        result = run_simulation(config_data, config_data['id'], start_date, end_date, model_path)
        results.append(result)
        
        # Save intermediate results
        if (i + 1) % 10 == 0:
            df_temp = pd.DataFrame(results)
            df_temp.to_csv(f'adaptive_sweep_intermediate_{i+1}.csv', index=False)
            log.info(f"Saved intermediate results after {i+1} simulations")
    
    # Save final results
    df_results = pd.DataFrame(results)
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_file = f'adaptive_parameter_sweep_{timestamp}.csv'
    df_results.to_csv(results_file, index=False)
    
    elapsed_time = time.time() - start_time
    log.info(f"Parameter sweep completed in {elapsed_time:.1f} seconds")
    log.info(f"Results saved to: {results_file}")
    
    # Analyze results
    analyze_sweep_results(df_results)
    
    return df_results

def analyze_sweep_results(df_results):
    """Analyze parameter sweep results"""
    
    log.info("🔍 Analyzing parameter sweep results...")
    
    # Filter successful runs
    successful_runs = df_results[df_results['success'] == True].copy()
    
    if len(successful_runs) == 0:
        log.error("No successful runs found!")
        return
    
    log.info(f"Successful runs: {len(successful_runs)}/{len(df_results)}")
    
    # Sort by total PnL
    top_configs = successful_runs.sort_values('total_pnl', ascending=False).head(10)
    
    log.info("\n🏆 TOP 10 CONFIGURATIONS BY PnL:")
    log.info("=" * 80)
    for idx, row in top_configs.iterrows():
        log.info(f"#{idx+1}: {row['description']}")
        log.info(f"   PnL: ${row['total_pnl']:.3f}, Trades: {row['total_trades']}, "
                f"Win Rate: {row['win_rate']:.1f}%, PF: {row['profit_factor']:.2f}")
    
    # Analyze by smoothing method
    log.info("\n📊 PERFORMANCE BY SMOOTHING METHOD:")
    log.info("=" * 50)
    method_analysis = successful_runs.groupby(successful_runs['description'].str.split('_').str[0]).agg({
        'total_pnl': ['mean', 'std', 'max'],
        'total_trades': 'mean',
        'win_rate': 'mean',
        'profit_factor': 'mean'
    }).round(3)
    
    for method in method_analysis.index:
        stats = method_analysis.loc[method]
        log.info(f"{method.upper()}:")
        log.info(f"   Avg PnL: ${stats[('total_pnl', 'mean')]:.3f} ± {stats[('total_pnl', 'std')]:.3f}")
        log.info(f"   Max PnL: ${stats[('total_pnl', 'max')]:.3f}")
        log.info(f"   Avg Trades: {stats[('total_trades', 'mean')]:.1f}")
        log.info(f"   Avg Win Rate: {stats[('win_rate', 'mean')]:.1f}%")
        log.info(f"   Avg Profit Factor: {stats[('profit_factor', 'mean')]:.2f}")
    
    # Best configuration recommendation
    best_config = top_configs.iloc[0]
    log.info(f"\n🎯 RECOMMENDED CONFIGURATION:")
    log.info(f"   Description: {best_config['description']}")
    log.info(f"   Expected PnL: ${best_config['total_pnl']:.3f}")
    log.info(f"   Expected Trades: {best_config['total_trades']}")
    log.info(f"   Expected Win Rate: {best_config['win_rate']:.1f}%")
    log.info(f"   Expected Profit Factor: {best_config['profit_factor']:.2f}")

def main():
    """Main function"""
    
    log.info("🚀 Starting adaptive parameter sweep...")
    
    try:
        results = run_parameter_sweep()
        log.info("✅ Parameter sweep completed successfully!")
        return results
        
    except Exception as e:
        log.error(f"❌ Parameter sweep failed: {e}", exc_info=True)
        return None

if __name__ == "__main__":
    results = main()
