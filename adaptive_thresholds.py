"""
Adaptive Threshold Tuning Module

This module provides dynamic threshold adjustment based on:
1. Signal confidence (from smoothing methods)
2. Market volatility (ATR-based)
3. Historical signal performance
4. Multi-factor scoring system
"""

import numpy as np
import pandas as pd
from typing import Dict, Any, Optional, Tuple
import logging

log = logging.getLogger(__name__)


class AdaptiveThresholdTuner:
    """
    Dynamically adjusts trading thresholds based on market conditions and signal quality
    """
    
    def __init__(self, base_long_threshold: float, base_short_threshold: float,
                 base_exit_threshold: float, **kwargs):
        """
        Initialize adaptive threshold tuner
        
        Args:
            base_long_threshold: Base long entry threshold
            base_short_threshold: Base short entry threshold  
            base_exit_threshold: Base exit threshold
            **kwargs: Configuration parameters
        """
        self.base_long_threshold = base_long_threshold
        self.base_short_threshold = base_short_threshold
        self.base_exit_threshold = base_exit_threshold
        
        # Configuration
        self.confidence_weight = kwargs.get('confidenceWeight', 0.5)
        self.volatility_weight = kwargs.get('volatilityWeight', 0.1)
        self.enabled = kwargs.get('adaptiveThresholdsEnabled', True)
        
        # State tracking
        self.signal_performance_history = []
        self.recent_atr_ratios = []
        self.recent_confidences = []
        
        # Performance tracking
        self.threshold_adjustments = []
        
    def calculate_adaptive_thresholds(self, signal_confidence: float, atr_ratio: float,
                                    recent_performance: Optional[Dict[str, float]] = None) -> Tuple[float, float, float]:
        """
        Calculate adaptive thresholds based on current market conditions
        
        Args:
            signal_confidence: Confidence from signal smoothing (0.0 to 1.0)
            atr_ratio: Current ATR / Average ATR
            recent_performance: Optional recent trading performance metrics
            
        Returns:
            Tuple of (adjusted_long_threshold, adjusted_short_threshold, adjusted_exit_threshold)
        """
        
        if not self.enabled:
            return self.base_long_threshold, self.base_short_threshold, self.base_exit_threshold
        
        # Store recent values for trend analysis
        self.recent_confidences.append(signal_confidence)
        self.recent_atr_ratios.append(atr_ratio)
        
        # Keep only recent history
        if len(self.recent_confidences) > 20:
            self.recent_confidences.pop(0)
        if len(self.recent_atr_ratios) > 20:
            self.recent_atr_ratios.pop(0)
        
        # Calculate adjustment factors
        confidence_adjustment = self._calculate_confidence_adjustment(signal_confidence)
        volatility_adjustment = self._calculate_volatility_adjustment(atr_ratio)
        performance_adjustment = self._calculate_performance_adjustment(recent_performance)
        
        # Combine adjustments
        total_long_adjustment = (
            confidence_adjustment * self.confidence_weight +
            volatility_adjustment * self.volatility_weight +
            performance_adjustment * 0.2
        )
        
        total_short_adjustment = total_long_adjustment  # Same adjustment for both directions
        total_exit_adjustment = total_long_adjustment * 0.5  # Less aggressive for exits
        
        # Apply adjustments to base thresholds
        adjusted_long = self.base_long_threshold + total_long_adjustment
        adjusted_short = self.base_short_threshold - total_long_adjustment  # Opposite direction
        adjusted_exit = self.base_exit_threshold + total_exit_adjustment
        
        # Ensure thresholds stay within reasonable bounds
        adjusted_long = np.clip(adjusted_long, 0.1, 0.95)
        adjusted_short = np.clip(adjusted_short, -0.95, -0.1)
        adjusted_exit = np.clip(adjusted_exit, 0.1, 0.95)
        
        # Log adjustment details
        adjustment_info = {
            'confidence_adj': confidence_adjustment,
            'volatility_adj': volatility_adjustment,
            'performance_adj': performance_adjustment,
            'total_adj': total_long_adjustment,
            'signal_confidence': signal_confidence,
            'atr_ratio': atr_ratio
        }
        
        self.threshold_adjustments.append(adjustment_info)
        if len(self.threshold_adjustments) > 100:
            self.threshold_adjustments.pop(0)
        
        log.info(f"🎯 ADAPTIVE THRESHOLDS:")
        log.info(f"   Base: L={self.base_long_threshold:.3f}, S={self.base_short_threshold:.3f}")
        log.info(f"   Adjusted: L={adjusted_long:.3f}, S={adjusted_short:.3f}")
        log.info(f"   Confidence: {signal_confidence:.3f} → adj={confidence_adjustment:.3f}")
        log.info(f"   Volatility: ATR ratio={atr_ratio:.3f} → adj={volatility_adjustment:.3f}")
        
        return adjusted_long, adjusted_short, adjusted_exit
    
    def _calculate_confidence_adjustment(self, signal_confidence: float) -> float:
        """
        Calculate threshold adjustment based on signal confidence
        
        Higher confidence → lower thresholds (easier to trigger)
        Lower confidence → higher thresholds (harder to trigger)
        """
        
        # Normalize confidence to adjustment range
        # confidence 1.0 → adjustment -0.2 (lower thresholds)
        # confidence 0.5 → adjustment 0.0 (no change)
        # confidence 0.0 → adjustment +0.3 (higher thresholds)
        
        confidence_adjustment = (0.5 - signal_confidence) * 0.5
        
        # Add trend component if we have history
        if len(self.recent_confidences) >= 5:
            recent_trend = np.mean(self.recent_confidences[-3:]) - np.mean(self.recent_confidences[-5:-2])
            confidence_adjustment += recent_trend * 0.1  # Small trend component
        
        return confidence_adjustment
    
    def _calculate_volatility_adjustment(self, atr_ratio: float) -> float:
        """
        Calculate threshold adjustment based on market volatility
        
        Higher volatility → lower thresholds (more opportunities)
        Lower volatility → higher thresholds (quality over quantity)
        """
        
        # Normalize ATR ratio to adjustment range
        # atr_ratio 2.0 → adjustment -0.15 (lower thresholds in high volatility)
        # atr_ratio 1.0 → adjustment 0.0 (no change at average volatility)
        # atr_ratio 0.5 → adjustment +0.1 (higher thresholds in low volatility)
        
        if atr_ratio > 1.0:
            # High volatility - lower thresholds
            volatility_adjustment = -(atr_ratio - 1.0) * 0.15
            volatility_adjustment = max(volatility_adjustment, -0.2)  # Cap at -0.2
        else:
            # Low volatility - higher thresholds
            volatility_adjustment = (1.0 - atr_ratio) * 0.1
            volatility_adjustment = min(volatility_adjustment, 0.15)  # Cap at +0.15
        
        return volatility_adjustment
    
    def _calculate_performance_adjustment(self, recent_performance: Optional[Dict[str, float]]) -> float:
        """
        Calculate threshold adjustment based on recent trading performance
        """
        
        if recent_performance is None:
            return 0.0
        
        # Extract performance metrics
        win_rate = recent_performance.get('win_rate', 0.5)
        avg_pnl = recent_performance.get('avg_pnl_per_trade', 0.0)
        trade_count = recent_performance.get('trade_count', 0)
        
        # Not enough trades for reliable adjustment
        if trade_count < 5:
            return 0.0
        
        # Performance-based adjustment
        performance_score = 0.0
        
        # Win rate component
        if win_rate > 0.6:
            performance_score -= 0.05  # Good win rate → lower thresholds
        elif win_rate < 0.4:
            performance_score += 0.1   # Poor win rate → higher thresholds
        
        # PnL component
        if avg_pnl > 0.01:
            performance_score -= 0.03  # Good PnL → lower thresholds
        elif avg_pnl < -0.005:
            performance_score += 0.05  # Poor PnL → higher thresholds
        
        return performance_score
    
    def get_threshold_statistics(self) -> Dict[str, Any]:
        """Get statistics about threshold adjustments"""
        
        if len(self.threshold_adjustments) == 0:
            return {'no_data': True}
        
        recent_adjustments = self.threshold_adjustments[-20:]  # Last 20 adjustments
        
        confidence_adjs = [adj['confidence_adj'] for adj in recent_adjustments]
        volatility_adjs = [adj['volatility_adj'] for adj in recent_adjustments]
        total_adjs = [adj['total_adj'] for adj in recent_adjustments]
        
        stats = {
            'adjustment_count': len(self.threshold_adjustments),
            'recent_confidence_adj': {
                'mean': np.mean(confidence_adjs),
                'std': np.std(confidence_adjs),
                'min': np.min(confidence_adjs),
                'max': np.max(confidence_adjs)
            },
            'recent_volatility_adj': {
                'mean': np.mean(volatility_adjs),
                'std': np.std(volatility_adjs),
                'min': np.min(volatility_adjs),
                'max': np.max(volatility_adjs)
            },
            'recent_total_adj': {
                'mean': np.mean(total_adjs),
                'std': np.std(total_adjs),
                'min': np.min(total_adjs),
                'max': np.max(total_adjs)
            }
        }
        
        return stats
    
    def log_threshold_exceedance(self, timestamp: pd.Timestamp, signal_value: float,
                               threshold_type: str, threshold_value: float,
                               exceeded: bool, log_file: Optional[str] = None):
        """
        Log when trading signals exceed thresholds
        
        Args:
            timestamp: Current timestamp
            signal_value: The signal value being compared
            threshold_type: "LONG" or "SHORT"
            threshold_value: The threshold value used
            exceeded: Whether threshold was exceeded
            log_file: Optional file to write logs to
        """
        
        comparison_op = ">=" if threshold_type == "LONG" else "<="
        status = "EXCEEDED" if exceeded else "NOT_EXCEEDED"
        
        log_message = (f"{timestamp.strftime('%Y-%m-%d %H:%M:%S')} | "
                      f"{threshold_type} | {signal_value:.6f} {comparison_op} {threshold_value:.6f} | "
                      f"{status}")
        
        log.info(f"🎯 THRESHOLD CHECK: {log_message}")
        
        if log_file:
            try:
                with open(log_file, 'a') as f:
                    f.write(log_message + "\n")
            except Exception as e:
                log.warning(f"Failed to write threshold log to {log_file}: {e}")
    
    def reset_performance_tracking(self):
        """Reset performance tracking for new trading session"""
        self.signal_performance_history.clear()
        self.threshold_adjustments.clear()
        log.info("🔄 Reset adaptive threshold performance tracking")


def create_threshold_tuner_from_config(config: Dict[str, Any]) -> AdaptiveThresholdTuner:
    """
    Create AdaptiveThresholdTuner from configuration
    
    Args:
        config: Configuration dictionary
        
    Returns:
        Configured AdaptiveThresholdTuner instance
    """
    
    trade_params = config.get('tradeParams', {})
    threshold_tuning = trade_params.get('thresholdTuning', {})
    
    base_long = trade_params.get('longEntryThreshold', 0.8)
    base_short = trade_params.get('shortEntryThreshold', -0.8)
    base_exit = trade_params.get('exitActionThreshold', 0.8)
    
    tuner = AdaptiveThresholdTuner(
        base_long_threshold=base_long,
        base_short_threshold=base_short,
        base_exit_threshold=base_exit,
        **threshold_tuning
    )
    
    return tuner
