#!/usr/bin/env python3
"""
DataProvider - Simple data provider for live trading initialization.
Primarily used for historical data loading during startup.
"""

import logging
from datetime import datetime, timedelta, timezone
from pathlib import Path
from typing import Dict, Any, Optional
import pandas as pd
import pyarrow.parquet as pq
import requests

log = logging.getLogger("DataProvider")

def safe_read_parquet(file_path, **kwargs):
    """
    Safe wrapper around pd.read_parquet that validates data quality.
    Detects and handles corrupted market data with 0.00000 prices.
    """
    try:
        df = pd.read_parquet(file_path, **kwargs)
        
        # Check if this is market OHLC data
        ohlc_cols = ['open', 'high', 'low', 'close']
        available_ohlc = [col for col in ohlc_cols if col in df.columns]
        
        if available_ohlc:
            # Check for corrupted 0.00000 prices in OHLC data
            for col in available_ohlc:
                zero_mask = (df[col] == 0.0) | (df[col].isna())
                if zero_mask.any():
                    corrupted_count = zero_mask.sum()
                    total_rows = len(df)
                    
                    if corrupted_count > 0:
                        log.warning(f"⚠️ DATA CORRUPTION DETECTED in {file_path}")
                        log.warning(f"   Column '{col}' has {corrupted_count}/{total_rows} corrupted (0.00000) values")
                        
                        # Show first few corrupted timestamps for debugging
                        if 'timestamp' in df.columns:
                            corrupted_rows = df[zero_mask]
                            for idx, row in corrupted_rows.head(3).iterrows():
                                timestamp = pd.to_datetime(row['timestamp'], utc=True) if pd.notna(row['timestamp']) else 'Unknown'
                                ohlc_vals = {c: row.get(c, 'N/A') for c in available_ohlc}
                                log.warning(f"   {timestamp}: {ohlc_vals}")
                        
                        # CRITICAL: Remove corrupted rows to prevent trading losses
                        # Only remove rows where ANY OHLC value is 0.0 or NaN
                        corruption_mask = pd.Series(False, index=df.index)
                        for ohlc_col in available_ohlc:
                            corruption_mask |= (df[ohlc_col] == 0.0) | (df[ohlc_col].isna())
                        
                        if corruption_mask.any():
                            clean_df = df[~corruption_mask].copy()
                            removed_count = len(df) - len(clean_df)
                            log.warning(f"   🧹 CLEANED: Removed {removed_count} corrupted rows")
                            log.warning(f"   ✅ SAFE DATA: {len(clean_df)} clean rows remaining")
                            df = clean_df
                        
                        # If too much data is corrupted, raise error
                        if len(df) < total_rows * 0.5:  # More than 50% corrupted
                            raise ValueError(f"File {file_path} has too much corrupted data ({corrupted_count}/{total_rows} rows). Cannot proceed safely.")
        
        return df
        
    except Exception as e:
        log.error(f"Error reading {file_path}: {e}")
        raise

class DataProvider:
    """
    Data provider for live trading system.
    Handles historical data loading for agent initialization and warm-up.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize DataProvider with configuration."""
        self.config = config
        self.symbol = config.get('dataProvider', {}).get('symbol', 'XRPUSDC')
        self.timeframe = config.get('primaryTimeframe', '5m')
        
        # Data directory setup
        data_dir_config = config.get('featureParquetDir', 'parquet')
        self.data_dir = Path(data_dir_config).expanduser()
        
        log.info(f"DataProvider initialized for {self.symbol}/{self.timeframe}")
        log.info(f"Data directory: {self.data_dir}")
    
    def load_historical_data(self, start_date: datetime, end_date: datetime,
                           lookback_hours: int = 24) -> Optional[pd.DataFrame]:
        """
        Load historical data for agent warm-up with fresh data fallback.

        Args:
            start_date: Start date for data loading
            end_date: End date for data loading
            lookback_hours: Additional hours to load before start_date for indicators

        Returns:
            DataFrame with historical data or None if failed
        """
        try:
            # Extend start date for indicator warm-up
            warm_start = start_date - timedelta(hours=lookback_hours)

            # Construct data path
            symbol_tf_dir = self.data_dir / self.symbol / self.timeframe
            if not symbol_tf_dir.exists():
                log.warning(f"Data directory not found: {symbol_tf_dir}")
                return None

            # Find available data files
            available_files = list(symbol_tf_dir.glob("*.parquet"))
            if not available_files:
                log.error(f"No parquet files found in {symbol_tf_dir}")
                return None

            # FIX 1: Sort files by date - NEWEST FIRST to prioritize fresh data
            available_files.sort(reverse=True)
            log.info(f"Found {len(available_files)} available data files in {symbol_tf_dir}")

            # Note: Historical data loading - we expect processed data to be available
            # Use prepare_historical_data.py to download raw data and compute features first

            # Load daily parquet files - try requested range first, then fallback to available data
            dfs = []
            current_date = warm_start.date()
            files_loaded = 0

            # FIX 2: Increase file limit for longer lookback periods (was 10, now 15)
            max_files = 15 if lookback_hours > 48 else 10

            # First pass: try to load requested date range
            while current_date <= end_date.date() and files_loaded < max_files:
                day_file = symbol_tf_dir / f"{current_date}.parquet"
                if day_file.exists():
                    try:
                        df = safe_read_parquet(day_file, engine="pyarrow")
                        if "timestamp" in df.columns:
                            df.set_index("timestamp", inplace=True)
                        df.index = pd.to_datetime(df.index, utc=True)
                        dfs.append(df)
                        files_loaded += 1
                        log.debug(f"Loaded {len(df)} rows from {day_file}")
                    except Exception as e:
                        log.warning(f"Error loading {day_file}: {e}")
                else:
                    if current_date >= start_date.date():
                        log.debug(f"Missing data file: {day_file}")

                current_date += timedelta(days=1)

            # FALLBACK: If no files found in requested range, load most recent available files
            if not dfs and available_files:
                log.warning(f"No data found in requested range {start_date.date()} to {end_date.date()}")
                log.info("🔄 FALLBACK: Loading most recent available historical data for indicators...")

                # Load the most recent files (up to 5 files for indicator calculation)
                recent_files = available_files[:5]  # Take first 5 files (newest due to reverse sort)
                for day_file in recent_files:
                    try:
                        df = safe_read_parquet(day_file, engine="pyarrow")
                        if "timestamp" in df.columns:
                            df.set_index("timestamp", inplace=True)
                        df.index = pd.to_datetime(df.index, utc=True)
                        dfs.append(df)
                        files_loaded += 1
                        log.info(f"✅ FALLBACK: Loaded {len(df)} rows from {day_file}")
                    except Exception as e:
                        log.warning(f"Error loading fallback file {day_file}: {e}")

            if not dfs:
                log.error("No historical data files could be loaded")
                return None

            # Combine all data
            combined_df = pd.concat(dfs).sort_index()

            # FIX 3: Filter data to requested time range (was missing!)
            combined_df = combined_df.loc[warm_start:end_date]

            # FIX 4: Sanity check for stale data
            log.info(f"LOADED DF range: {combined_df.index[0]} … {combined_df.index[-1]}")
            if combined_df.index[-1] < end_date - timedelta(minutes=10):
                log.warning("‼️ Historical data is stale – live_trading will start with old candles!")
                log.warning(f"   Last data: {combined_df.index[-1]}, Expected: {end_date}")

                # FIX 5: Try to fetch fresh data via REST API
                log.info("⏩ Missing fresh data, attempting to pull from REST API...")
                fresh_data = self._fetch_fresh_data_rest(combined_df.index[-1], end_date)
                if fresh_data is not None and not fresh_data.empty:
                    log.info(f"✅ Fetched {len(fresh_data)} fresh bars from REST API")
                    combined_df = pd.concat([combined_df, fresh_data]).sort_index()
                    # Remove duplicates if any
                    combined_df = combined_df[~combined_df.index.duplicated(keep='last')]
                else:
                    log.warning("❌ Failed to fetch fresh data from REST API")

            if len(combined_df) > 0:
                log.info(f"✅ Loaded {len(combined_df)} historical records from {files_loaded} files")
                log.info(f"   Final data range: {combined_df.index[0]} to {combined_df.index[-1]}")
                return combined_df
            else:
                return None

        except Exception as e:
            log.error(f"Error loading historical data: {e}")
            return None

    def _fetch_fresh_data_rest(self, last_timestamp: datetime, end_date: datetime) -> Optional[pd.DataFrame]:
        """
        Fetch fresh data via REST API when historical files are stale.

        Args:
            last_timestamp: Last timestamp in historical data
            end_date: Target end date

        Returns:
            DataFrame with fresh data or None if failed
        """
        try:
            # Calculate the gap we need to fill
            gap_start = last_timestamp + timedelta(minutes=5)  # Next 5m bar after last data
            gap_minutes = int((end_date - gap_start).total_seconds() / 60)

            if gap_minutes <= 0:
                log.debug("No gap to fill - historical data is up to date")
                return None

            log.info(f"Attempting to fill {gap_minutes} minute gap from {gap_start} to {end_date}")

            # For now, return None - this would be implemented with actual REST API calls
            # TODO: Implement CoinAPI or Binance REST API calls here
            log.warning("REST API fallback not yet implemented - using WebSocket data only")
            return None

        except Exception as e:
            log.error(f"Error fetching fresh data via REST: {e}")
            return None

    def _download_today_parquet(self, today_file: Path, symbol: str, timeframe: str) -> bool:
        """
        Download today's data from CoinAPI REST and save as parquet file.

        Args:
            today_file: Path where to save today's parquet file
            symbol: Trading symbol (e.g., 'XRPUSDC')
            timeframe: Timeframe (e.g., '5m')

        Returns:
            True if successful, False otherwise
        """
        try:

            # Convert timeframe to CoinAPI period_id
            period_map = {
                '1m': '1MIN',
                '5m': '5MIN',
                '15m': '15MIN',
                '1h': '1HRS',
                '4h': '4HRS',
                '1d': '1DAY'
            }

            period_id = period_map.get(timeframe, '5MIN')

            # Calculate today's start (00:00 UTC)
            today_start = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)

            # CoinAPI symbol format
            coinapi_symbol = f"BINANCEFTS_PERP_{symbol.replace('USDC', '_USDC').replace('USDT', '_USDT')}"

            # CoinAPI REST endpoint
            url = "https://rest.coinapi.io/v1/ohlcv/history"
            headers = {
                "X-CoinAPI-Key": "a49bb33b-6504-4776-acc2-25c1026b1cef"  # Use your actual API key
            }

            params = {
                "symbol_id": coinapi_symbol,
                "period_id": period_id,
                "time_start": today_start.isoformat(),
                "limit": 400  # Increased from 300 to 400 to ensure enough data for warm-up indicators
            }

            log.info(f"⬇️ Downloading today's {timeframe} data for {symbol} from CoinAPI REST...")
            log.info(f"   URL: {url}")
            log.info(f"   Symbol: {coinapi_symbol}")
            log.info(f"   Period: {period_id}")
            log.info(f"   Start: {today_start}")

            response = requests.get(url, headers=headers, params=params, timeout=30)
            response.raise_for_status()

            data = response.json()

            if not data:
                log.warning(f"No data received from CoinAPI for {symbol} {timeframe}")
                return False

            # Convert to DataFrame
            rows = []
            for bar in data:
                timestamp = pd.to_datetime(bar['time_period_start'], utc=True)
                rows.append({
                    'timestamp': timestamp,
                    'open': float(bar['price_open']),
                    'high': float(bar['price_high']),
                    'low': float(bar['price_low']),
                    'close': float(bar['price_close']),
                    'volume': float(bar['volume_traded'])
                })

            if not rows:
                log.warning(f"No valid bars parsed from CoinAPI response for {symbol} {timeframe}")
                return False

            df = pd.DataFrame(rows)
            df.set_index('timestamp', inplace=True)
            df.sort_index(inplace=True)

            # Ensure directory exists
            today_file.parent.mkdir(parents=True, exist_ok=True)

            # Save as parquet
            df.to_parquet(today_file, engine='pyarrow')

            log.info(f"✅ Downloaded and saved {len(df)} today's {timeframe} bars to {today_file}")
            log.info(f"   Data range: {df.index[0]} to {df.index[-1]}")

            return True

        except Exception as e:
            log.error(f"Error downloading today's parquet for {symbol} {timeframe}: {e}")
            return False
    
    def get_latest_price(self, symbol: Optional[str] = None) -> Optional[float]:
        """
        Get latest price for symbol (placeholder for live implementation).
        In live trading, this would query an exchange API.
        """
        try:
            # For now, try to get from most recent historical data
            if symbol is None:
                symbol = self.symbol
            
            end_time = datetime.now(timezone.utc)
            start_time = end_time - timedelta(hours=1)
            
            recent_data = self.load_historical_data(start_time, end_time, lookback_hours=0)
            if recent_data is not None and not recent_data.empty:
                latest_price = recent_data['close'].iloc[-1]
                log.debug(f"Latest price for {symbol}: {latest_price}")
                return float(latest_price)
            
        except Exception as e:
            log.warning(f"Error getting latest price for {symbol}: {e}")
        
        return None
    
    def validate_data_availability(self, start_date: datetime, end_date: datetime) -> bool:
        """
        Check if data is available for the requested time range.
        
        Returns:
            True if sufficient data is available, False otherwise
        """
        try:
            symbol_tf_dir = self.data_dir / self.symbol / self.timeframe
            if not symbol_tf_dir.exists():
                log.error(f"Data directory not found: {symbol_tf_dir}")
                return False
            
            # Check if we have at least some files in the date range
            current_date = start_date.date()
            found_files = 0
            total_days = (end_date.date() - start_date.date()).days + 1
            
            while current_date <= end_date.date():
                day_file = symbol_tf_dir / f"{current_date}.parquet"
                if day_file.exists():
                    found_files += 1
                current_date += timedelta(days=1)
            
            coverage_ratio = found_files / total_days if total_days > 0 else 0
            log.info(f"Data coverage: {found_files}/{total_days} days ({coverage_ratio:.1%})")
            
            # Require at least 80% coverage
            return coverage_ratio >= 0.8
            
        except Exception as e:
            log.error(f"Error validating data availability: {e}")
            return False
    
    def get_symbol_info(self) -> Dict[str, Any]:
        """
        Get symbol information for trading.
        """
        return {
            'symbol': self.symbol,
            'timeframe': self.timeframe,
            'data_dir': str(self.data_dir),
            'base_asset': self.symbol[:-4] if len(self.symbol) > 4 else self.symbol[:3],
            'quote_asset': self.symbol[-4:] if len(self.symbol) > 4 else self.symbol[3:]
        }