#!/usr/bin/env python3
"""
Exact copy of live_trading.py logic using simulate_trading_new.py functions
This ensures 100% identical behavior between simulation and live trading
"""
import os, sys, json, logging, argparse
from pathlib import Path
from datetime import datetime, timezone, timedelta
import pandas as pd
import numpy as np
import torch
import gymnasium as gym

# Import our modules
from indicators import calculate_and_merge_indicators
from popart_sac import PopArtSAC
from agent import SimpleCNN1D, SafeReplayBuffer
from stable_baselines3.common.vec_env import VecNormalize, DummyVecEnv

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(name)s] [%(levelname)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
    handlers=[logging.StreamHandler(sys.stdout)]
)
log = logging.getLogger("SimulateLiveTradingExact")

def load_config(config_path: Path) -> dict:
    """Load configuration from JSON file (copied from simulate_trading_new.py)"""
    with open(config_path, 'r') as f:
        content = f.read()
        # Replace environment variables
        import re
        def replace_env_var(match):
            var_name = match.group(1)
            return os.environ.get(var_name, match.group(0))
        content = re.sub(r'\$\{([^}]+)\}', replace_env_var, content)
        config = json.loads(content)
    
    log.info(f"Konfigurácia načítaná pre symbol: {config.get('symbol', 'N/A')}")
    return config

def load_backtest_data(config: dict, start_date: datetime, end_date: datetime, 
                      load_second_data: bool = True, use_1s_decisions: bool = False) -> dict:
    """
    Load historical data exactly like simulate_trading_new.py
    """
    symbol = config.get('symbol')
    if not symbol:
        raise ValueError("Symbol not found in config")
    
    log.info(f"Načítavam dáta pre {symbol} od {start_date} do {end_date}")
    
    # Load 5m data
    parquet_dir_5m = Path(f"parquet_processed/{symbol}/5m")
    if not parquet_dir_5m.exists():
        raise FileNotFoundError(f"5m parquet directory not found: {parquet_dir_5m}")
    
    # Load 1s data if needed
    parquet_dir_1s = Path(f"parquet_processed/{symbol}/1s") if load_second_data else None
    
    all_data_5m = []
    all_data_1s = []
    
    current_date = start_date.date()
    end_date_obj = end_date.date()
    
    while current_date <= end_date_obj:
        # Load 5m data
        parquet_file_5m = parquet_dir_5m / f"{current_date}.parquet"
        if parquet_file_5m.exists():
            df_5m = pd.read_parquet(parquet_file_5m)
            if 'timestamp' in df_5m.columns:
                df_5m['timestamp'] = pd.to_datetime(df_5m['timestamp'])
                df_5m = df_5m.set_index('timestamp')
            if not isinstance(df_5m.index, pd.DatetimeIndex):
                df_5m.index = pd.to_datetime(df_5m.index)
            if df_5m.index.tz is None:
                df_5m.index = df_5m.index.tz_localize('UTC')
            else:
                df_5m.index = df_5m.index.tz_convert('UTC')
            df_5m = df_5m[(df_5m.index >= start_date) & (df_5m.index <= end_date)]
            if not df_5m.empty:
                all_data_5m.append(df_5m)
        
        # Load 1s data if needed
        if load_second_data and parquet_dir_1s:
            parquet_file_1s = parquet_dir_1s / f"{current_date}.parquet"
            if parquet_file_1s.exists():
                df_1s = pd.read_parquet(parquet_file_1s)
                if 'timestamp' in df_1s.columns:
                    df_1s['timestamp'] = pd.to_datetime(df_1s['timestamp'])
                    df_1s = df_1s.set_index('timestamp')
                if not isinstance(df_1s.index, pd.DatetimeIndex):
                    df_1s.index = pd.to_datetime(df_1s.index)
                if df_1s.index.tz is None:
                    df_1s.index = df_1s.index.tz_localize('UTC')
                else:
                    df_1s.index = df_1s.index.tz_convert('UTC')
                df_1s = df_1s[(df_1s.index >= start_date) & (df_1s.index <= end_date)]
                if not df_1s.empty:
                    all_data_1s.append(df_1s)
        
        current_date += timedelta(days=1)
    
    if not all_data_5m:
        raise ValueError(f"No 5m data found for {symbol} in date range")
    
    # Combine data
    combined_5m = pd.concat(all_data_5m, axis=0).sort_index()
    combined_1s = pd.concat(all_data_1s, axis=0).sort_index() if all_data_1s else pd.DataFrame()
    
    log.info(f"Načítané: {len(combined_5m)} 5m barov, {len(combined_1s)} 1s barov")
    
    # Prepare data dict exactly like simulate_trading_new.py
    data_dict = {
        'primary': combined_5m,
        'second': combined_1s if not combined_1s.empty else None,
        'use_1s_decisions': use_1s_decisions
    }
    
    return data_dict

def get_state(df_features: pd.DataFrame, current_step_index: int, lookback: int,
              current_pos: int, entry_price: float, current_price: float,
              current_sl_price: float, current_tp_price: float,
              open_idx_sim: int, current_atr: float,
              inactivity_limit: int, tsl_enabled: bool,
              use_1s_decisions: bool = False, features_5m: pd.DataFrame = None,
              main_timestamps: pd.Index = None, current_time: pd.Timestamp = None) -> np.ndarray:
    """
    EXACT COPY of get_state function from simulate_trading_new.py
    """
    if current_step_index < lookback - 1:
        raise IndexError(f"Nedostatok histórie ({current_step_index+1}) pre lookback={lookback}.")

    if use_1s_decisions:
        # Real-time forward-fill mode - vytvoríme features pre lookback window
        if features_5m is None or main_timestamps is None or current_time is None:
            raise ValueError("V 1s mode sú potrebné features_5m, main_timestamps a current_time")
        
        # Získame lookback window timestamps
        start_idx = current_step_index - lookback + 1
        end_idx = current_step_index + 1
        lookback_timestamps = main_timestamps[start_idx:end_idx]
        
        if len(lookback_timestamps) != lookback:
            raise ValueError(f"Získaných {len(lookback_timestamps)} timestamps namiesto {lookback}")
        
        # Pre každý timestamp v lookback window vytvoríme real-time forward-fill features
        frame_data = []
        for ts in lookback_timestamps:
            # Nájdeme najnovšie dostupné 5m features bez look-ahead
            mask_5m = features_5m.index <= ts
            available_5m = features_5m[mask_5m]
            
            if available_5m.empty:
                # Ak nie sú dostupné žiadne 5m features, použijeme nuly
                frame_data.append(np.zeros(len(features_5m.columns), dtype=np.float32))
            else:
                # Použijeme najnovšie dostupné 5m features (forward-fill)
                latest_features = available_5m.iloc[-1].values.astype(np.float32)
                frame_data.append(latest_features)
        
        frame = np.array(frame_data, dtype=np.float32)
    else:
        # Štandardný 5m mode
        frame = df_features.iloc[current_step_index - lookback + 1:current_step_index + 1].values.astype(np.float32)
    
    # Clip frame values
    frame_flat_clipped = np.clip(frame.flatten(), -10, 10)
    
    # --- TRADE META FEATURES (7) ---
    safe_current_price = current_price if np.isfinite(current_price) and current_price > 0 else 1.0
    safe_entry_price = entry_price if np.isfinite(entry_price) and entry_price > 0 else safe_current_price
    
    # 1. PnL Normalizované
    pnl_norm = 0.0
    if current_pos != 0 and safe_entry_price > 0:
        pnl_raw = (safe_current_price - safe_entry_price) * current_pos
        pnl_norm = np.tanh(pnl_raw / safe_current_price)
    
    # 2. Trade Duration Normalizované
    trade_duration = 0.0
    if current_pos != 0 and open_idx_sim >= 0:
        duration_steps = current_step_index - open_idx_sim
        trade_duration = np.tanh(duration_steps / inactivity_limit) if inactivity_limit > 0 else 0.0
    
    # 3. Profit Status
    profit_status = 0.0
    if current_pos != 0 and entry_price > 0:
        profit_status = np.sign((safe_current_price - safe_entry_price) * current_pos)

    # 4. & 5. Vzdialenosť od TP a SL Normalizovaná
    tp_distance = 0.0
    sl_distance = 0.0
    if current_pos != 0 and np.isfinite(current_tp_price) and np.isfinite(current_sl_price) and current_tp_price > 0 and current_sl_price > 0:
        tp_norm_factor = safe_current_price * 0.01 # Normalizácia 1%
        sl_norm_factor = safe_current_price * 0.01
        if abs(tp_norm_factor) > 1e-9:
            tp_distance = np.tanh(abs(current_tp_price - safe_current_price) / tp_norm_factor) * np.sign(current_tp_price - safe_current_price) * current_pos
        if abs(sl_norm_factor) > 1e-9:
            sl_distance = np.tanh(abs(current_sl_price - safe_current_price) / sl_norm_factor) * np.sign(current_sl_price - safe_current_price) * current_pos

    # 6. PnL / ATR Ratio Normalizované
    pnl_atr_ratio = 0.0
    if current_pos != 0 and tsl_enabled and current_atr > 1e-9 and entry_price > 0:
        pnl_atr_ratio = np.tanh(((safe_current_price - safe_entry_price) * current_pos) / current_atr)

    # --- TIME FEATURES (4) ---
    if use_1s_decisions:
        cur_timestamp = current_time
    else:
        cur_timestamp = df_features.index[current_step_index]
    
    cur_hour = cur_timestamp.hour
    dow = cur_timestamp.dayofweek        # 0=Mon … 6=Sun

    time_feats = [
        np.sin(cur_hour / 24 * 2 * np.pi),
        np.cos(cur_hour / 24 * 2 * np.pi),
        np.sin(dow / 7 * 2 * np.pi),
        np.cos(dow / 7 * 2 * np.pi),
    ]

    # --- Spojenie všetkých častí ---
    state_vector = np.concatenate((
        frame_flat_clipped,           # 1440 prvkov (48 features × 30 lookback)
        [
            float(current_pos),       # 1
            pnl_norm,                 # 2
            trade_duration,           # 3
            profit_status,            # 4
            tp_distance,              # 5
            sl_distance,              # 6
            pnl_atr_ratio             # 7
        ],
        time_feats                   # 4 time features
    )).astype(np.float32)             # Celkom 1440 + 7 + 4 = 1451 prvkov

    return state_vector

class ExactTradeExecutor:
    """
    Exact copy of trading logic from simulate_trading_new.py
    """
    def __init__(self, config):
        self.config = config

        # Trading state
        self.position = 0  # -1, 0, 1
        self.entry_price = 0.0
        self.entry_idx = -1
        self.current_sl_price = 0.0
        self.current_tp_price = 0.0
        self.tsl_peak_price = 0.0

        # Account
        self.equity_total = config.get('account', {}).get('initialEquity', 100.0)
        self.balance = self.equity_total

        # Trading parameters
        trade_params = config.get("tradeParams", {})
        self.entry_thr = trade_params.get('entryActionThreshold', 0.7)
        self.long_entry_thr = trade_params.get('longEntryThreshold', self.entry_thr)
        self.short_entry_thr = trade_params.get('shortEntryThreshold', self.entry_thr)
        self.exit_thr = trade_params.get('exitActionThreshold', 0.7)
        self.risk_per_trade = trade_params.get('riskPerTrade', 0.01)
        self.min_sl_distance_percent = trade_params.get('minSLDistancePercent', 2.0) / 100.0
        self.rr_target = trade_params.get('rrTarget', 2.0)

        # TSL parameters
        tsl_params = config.get("tslParams", {})
        self.tsl_enabled = tsl_params.get('enabled', True)
        self.activate_atr_multiplier = tsl_params.get('activateAtrMultiplier', 0.5)
        self.trail_atr_multiplier = tsl_params.get('trailAtrMultiplier', 0.2)
        self.min_atr_percent = tsl_params.get('minAtrPercent', 0.3) / 100.0
        self.min_sl_distance_atr = tsl_params.get('minSLDistanceAtr', 2.0)

        # Trade tracking
        self.trades = []
        self.daily_trades = 0
        self.daily_pnl = 0.0
        self.current_day = None

        # Risk management
        risk_mgmt = config.get("riskManagement", {})
        self.max_daily_trades = risk_mgmt.get('maxDailyTrades', 10)
        self.max_daily_loss = risk_mgmt.get('maxDailyLoss', 0.05)

    def execute_decision(self, action, current_price, current_time, current_atr, current_step_index):
        """Execute trading decision exactly like simulate_trading_new.py"""
        entry_sig = action[0]
        exit_sig = action[3] if len(action) > 3 else 0.0

        # Daily reset
        cur_day = current_time.date()
        if self.current_day != cur_day:
            self.current_day = cur_day
            self.daily_trades = 0
            self.daily_pnl = 0.0

        # Risk management checks
        if self.daily_trades >= self.max_daily_trades:
            return  # Max daily trades reached

        if self.daily_pnl <= -self.max_daily_loss * self.equity_total:
            return  # Max daily loss reached

        # Entry logic
        if self.position == 0:
            triggered_pos = 0
            if entry_sig > self.long_entry_thr:
                triggered_pos = 1
            elif entry_sig < -self.short_entry_thr:
                triggered_pos = -1

            if triggered_pos != 0:
                # Calculate position size
                risk_amount = self.equity_total * self.risk_per_trade

                # Calculate SL distance
                sl_distance_percent = max(self.min_sl_distance_percent, current_atr * self.min_sl_distance_atr / current_price)
                sl_distance = current_price * sl_distance_percent

                if sl_distance > 0:
                    position_size = risk_amount / sl_distance

                    # Enter position
                    self.position = triggered_pos
                    self.entry_price = current_price
                    self.entry_idx = current_step_index

                    # Set SL and TP
                    if triggered_pos == 1:  # Long
                        self.current_sl_price = current_price - sl_distance
                        self.current_tp_price = current_price + (sl_distance * self.rr_target)
                    else:  # Short
                        self.current_sl_price = current_price + sl_distance
                        self.current_tp_price = current_price - (sl_distance * self.rr_target)

                    self.tsl_peak_price = current_price

                    log.info(f"📈 Entry: {triggered_pos} @ {current_price:.4f}, SL: {self.current_sl_price:.4f}, TP: {self.current_tp_price:.4f}")

        # Exit logic
        elif self.position != 0:
            exit_triggered = False
            exit_reason = ""
            exit_price = current_price

            # SL/TP check
            if self.position == 1:  # Long
                if current_price <= self.current_sl_price:
                    exit_triggered = True
                    exit_reason = "SL"
                    exit_price = self.current_sl_price
                elif current_price >= self.current_tp_price:
                    exit_triggered = True
                    exit_reason = "TP"
                    exit_price = self.current_tp_price
            else:  # Short
                if current_price >= self.current_sl_price:
                    exit_triggered = True
                    exit_reason = "SL"
                    exit_price = self.current_sl_price
                elif current_price <= self.current_tp_price:
                    exit_triggered = True
                    exit_reason = "TP"
                    exit_price = self.current_tp_price

            # TSL logic
            if not exit_triggered and self.tsl_enabled and current_atr > 0:
                # Update peak price
                if self.position == 1 and current_price > self.tsl_peak_price:
                    self.tsl_peak_price = current_price
                elif self.position == -1 and current_price < self.tsl_peak_price:
                    self.tsl_peak_price = current_price

                # Check TSL activation
                profit = (self.tsl_peak_price - self.entry_price) * self.position
                activation_threshold = current_atr * self.activate_atr_multiplier

                if profit >= activation_threshold:
                    # TSL is active, calculate trailing SL
                    trail_distance = max(
                        current_atr * self.trail_atr_multiplier,
                        current_price * self.min_atr_percent
                    )

                    if self.position == 1:
                        new_sl = self.tsl_peak_price - trail_distance
                        if new_sl > self.current_sl_price:
                            self.current_sl_price = new_sl
                    else:
                        new_sl = self.tsl_peak_price + trail_distance
                        if new_sl < self.current_sl_price:
                            self.current_sl_price = new_sl

            # Agent exit signal
            if not exit_triggered and abs(exit_sig) > self.exit_thr:
                exit_triggered = True
                exit_reason = "AGENT"
                exit_price = current_price

            if exit_triggered:
                # Calculate PnL
                pnl = (exit_price - self.entry_price) * self.position
                pnl_percent = pnl / self.entry_price * 100

                # Update account
                self.equity_total += pnl
                self.balance = self.equity_total
                self.daily_pnl += pnl
                self.daily_trades += 1

                # Record trade
                trade = {
                    'entry_time': current_time,
                    'exit_time': current_time,
                    'entry_price': self.entry_price,
                    'exit_price': exit_price,
                    'direction': self.position,
                    'pnl': pnl,
                    'pnl_percent': pnl_percent,
                    'exit_reason': exit_reason,
                    'duration_steps': current_step_index - self.entry_idx
                }
                self.trades.append(trade)

                log.info(f"📉 Exit: {exit_reason} @ {exit_price:.4f}, PnL: ${pnl:.2f} ({pnl_percent:.2f}%)")

                # Reset position
                self.position = 0
                self.entry_price = 0.0
                self.entry_idx = -1
                self.current_sl_price = 0.0
                self.current_tp_price = 0.0
                self.tsl_peak_price = 0.0

def run_exact_simulation(config: dict, start_date: str, end_date: str,
                        out_trades: str = None, out_equity: str = None):
    """
    Run simulation with EXACT logic from simulate_trading_new.py
    """
    log.info(f"🚀 Starting EXACT simulation from {start_date} to {end_date}")

    # Parse dates
    start_dt = datetime.strptime(start_date, "%Y-%m-%d").replace(tzinfo=timezone.utc)
    end_dt = datetime.strptime(end_date, "%Y-%m-%d").replace(tzinfo=timezone.utc) + timedelta(days=1) - timedelta(seconds=1)

    # Load data exactly like simulate_trading_new.py
    use_1s_decisions = config.get("use_1s_decisions", False)
    data_dict = load_backtest_data(config, start_dt, end_dt, load_second_data=use_1s_decisions, use_1s_decisions=use_1s_decisions)

    # Calculate indicators exactly like simulate_trading_new.py
    log.info("📊 Calculating indicators...")
    if use_1s_decisions:
        indicators_data = {'5m': data_dict['primary'], '1s': data_dict['second']}
    else:
        indicators_data = {'5m': data_dict['primary']}

    df_features, calculated_features = calculate_and_merge_indicators(
        indicators_data, config, skip_hmm=True, hmm_model_external=None, hmm_scaler_external=None
    )

    log.info(f"📊 Features calculated: {df_features.shape}")

    # Load model exactly like simulate_trading_new.py
    log.info("🤖 Loading model...")
    model_files = list(Path(".").glob("sac_*_steps.zip"))
    if not model_files:
        raise FileNotFoundError("No SAC model files found!")

    latest_model = sorted(model_files, key=lambda x: int(x.stem.split('_')[1]))[-1]
    log.info(f"Using model: {latest_model}")

    # Setup model loading exactly like simulate_trading_new.py
    feature_cols = config["envSettings"]["feature_columns"]
    lookback = config["envSettings"].get("state_lookback", 30)
    expected_obs_size = len(feature_cols) * lookback + 11

    device = "cuda" if torch.cuda.is_available() else "cpu"
    training_settings = config["trainingSettings"]

    policy_kwargs = {
        "net_arch": training_settings["netArch"],
        "features_extractor_class": SimpleCNN1D,
        "features_extractor_kwargs": {
            **training_settings["featureExtractorKwargs"],
            "feature_names": feature_cols,
            "meta_len": 11
        }
    }

    custom_objects = {
        "buffer_size": training_settings["bufferSize"],
        "policy_kwargs": policy_kwargs,
        "replay_buffer_class": SafeReplayBuffer,
        "learning_rate": 0.0001
    }

    model = PopArtSAC.load(latest_model, device=device, custom_objects=custom_objects)

    # Load VecNormalize exactly like simulate_trading_new.py
    vecnorm_path = latest_model.with_suffix('.vecnorm.pkl')
    if not vecnorm_path.exists():
        model_stem = latest_model.stem.replace('_steps', '')
        vecnorm_path = latest_model.parent / f"{model_stem}.vecnorm.pkl"

    if vecnorm_path.exists():
        log.info(f"Loading VecNormalize: {vecnorm_path}")
        import gymnasium as gym

        class DummyEnv(gym.Env):
            def __init__(self):
                super().__init__()
                self.observation_space = gym.spaces.Box(low=-np.inf, high=np.inf, shape=(expected_obs_size,), dtype=np.float32)
                self.action_space = gym.spaces.Box(low=-1, high=1, shape=(4,), dtype=np.float32)
            def reset(self, **kwargs):
                return np.zeros(expected_obs_size, dtype=np.float32), {}
            def step(self, action):
                return np.zeros(expected_obs_size, dtype=np.float32), 0.0, False, False, {}

        dummy_env = DummyVecEnv([lambda: DummyEnv()])
        vecnorm = VecNormalize.load(vecnorm_path, dummy_env)
        vecnorm.training = False
        log.info("✅ VecNormalize loaded")
    else:
        log.warning("VecNormalize not found - using identity normalization")
        vecnorm = None

    log.info(f"✅ Model loaded: {model.observation_space.shape}")

    # Prepare data exactly like simulate_trading_new.py
    log.info("📊 Preparing data for simulation...")

    # Extract only required features
    missing_cols = [col for col in feature_cols if col not in df_features.columns]
    if missing_cols:
        log.warning(f"Missing {len(missing_cols)} columns → filled with zeros")
        for col in missing_cols:
            df_features[col] = 0.0

    # Select and prepare features
    df_features = df_features[feature_cols].ffill().fillna(0).astype("float32")
    log.info(f"📊 Features prepared: {df_features.shape}")

    # Initialize trade executor
    te = ExactTradeExecutor(config)

    # Prepare simulation data
    if use_1s_decisions:
        main_data = data_dict['second']  # 1s data for decisions
        features_5m = df_features  # 5m features
        log.info(f"🔄 1s decisions mode: {len(main_data)} 1s bars, {len(features_5m)} 5m features")
    else:
        main_data = df_features  # 5m data for both
        features_5m = None
        log.info(f"🔄 5m decisions mode: {len(main_data)} bars")

    # Run simulation exactly like simulate_trading_new.py
    log.info("🔄 Starting simulation loop...")
    equity_curve = []

    start_idx = lookback
    total_steps = len(main_data) - start_idx
    inactivity_limit = config.get("tradeParams", {}).get("inactivityLimit", 100)

    for i in range(start_idx, len(main_data)):
        current_time = main_data.index[i]
        current_price = main_data.iloc[i]['close']
        current_atr = df_features.iloc[-1]['ATR_14'] if 'ATR_14' in df_features.columns else 0.01

        try:
            # Get state exactly like simulate_trading_new.py
            state = get_state(
                df_features=df_features,
                current_step_index=i if not use_1s_decisions else len(df_features) - 1,
                lookback=lookback,
                current_pos=te.position,
                entry_price=te.entry_price,
                current_price=current_price,
                current_sl_price=te.current_sl_price,
                current_tp_price=te.current_tp_price,
                open_idx_sim=te.entry_idx,
                current_atr=current_atr,
                inactivity_limit=inactivity_limit,
                tsl_enabled=te.tsl_enabled,
                use_1s_decisions=use_1s_decisions,
                features_5m=features_5m,
                main_timestamps=main_data.index,
                current_time=current_time
            )

            # Apply VecNormalize exactly like simulate_trading_new.py
            state_input = np.expand_dims(state, axis=0)
            if vecnorm is not None:
                state_input = vecnorm.normalize_obs(state_input)

            # Get model prediction
            action, _ = model.predict(state_input[0], deterministic=True)

            # Execute trading decision
            te.execute_decision(action, current_price, current_time, current_atr, i)

            # Track equity
            unrealized_pnl = 0.0
            if te.position != 0 and te.entry_price > 0:
                unrealized_pnl = (current_price - te.entry_price) * te.position

            current_equity = te.equity_total + unrealized_pnl
            equity_curve.append({
                'timestamp': current_time,
                'equity': current_equity,
                'balance': te.equity_total,
                'position': te.position,
                'unrealized_pnl': unrealized_pnl
            })

            # Progress logging
            if i % 1000 == 0:
                progress = (i - start_idx) / total_steps * 100
                log.info(f"📊 Progress: {progress:.1f}% - Equity: ${current_equity:.2f}, Trades: {len(te.trades)}")

        except Exception as e:
            log.error(f"Error at step {i}: {e}")
            continue

    # Force close position at end (exactly like simulate_trading_new.py)
    if te.position != 0:
        final_price = main_data.iloc[-1]['close']
        final_pnl = (final_price - te.entry_price) * te.position
        te.equity_total += final_pnl

        trade = {
            'entry_time': main_data.index[te.entry_idx] if te.entry_idx >= 0 else main_data.index[-1],
            'exit_time': main_data.index[-1],
            'entry_price': te.entry_price,
            'exit_price': final_price,
            'direction': te.position,
            'pnl': final_pnl,
            'pnl_percent': final_pnl / te.entry_price * 100,
            'exit_reason': 'FORCED_CLOSE',
            'duration_steps': len(main_data) - 1 - te.entry_idx
        }
        te.trades.append(trade)
        log.warning(f"Forced close position at end: {te.position} @ {final_price:.4f}")

        te.position = 0

    log.info("✅ Simulation completed!")

    # Save results exactly like simulate_trading_new.py
    if out_trades and te.trades:
        trades_df = pd.DataFrame(te.trades)
        trades_df.to_csv(out_trades, index=False)
        log.info(f"💾 Trades saved to: {out_trades}")

    if out_equity and equity_curve:
        equity_df = pd.DataFrame(equity_curve)
        equity_df.set_index('timestamp', inplace=True)
        equity_df.to_csv(out_equity)
        log.info(f"💾 Equity curve saved to: {out_equity}")

    # Print results exactly like simulate_trading_new.py
    total_pnl = sum(trade['pnl'] for trade in te.trades)
    winning_trades = sum(1 for trade in te.trades if trade['pnl'] > 0)
    losing_trades = sum(1 for trade in te.trades if trade['pnl'] < 0)
    win_rate = winning_trades / len(te.trades) * 100 if te.trades else 0

    log.info("=" * 60)
    log.info("🏁 EXACT SIMULATION RESULTS")
    log.info("=" * 60)
    log.info(f"📊 Total trades: {len(te.trades)}")
    log.info(f"💰 Final equity: ${te.equity_total:.2f}")
    log.info(f"📈 Total PnL: ${total_pnl:.2f}")
    log.info(f"🎯 Win rate: {win_rate:.1f}% ({winning_trades}/{len(te.trades)})")
    log.info(f"📊 Return: {((te.equity_total / config['account']['initialEquity']) - 1) * 100:.2f}%")

    return {
        'trades': te.trades,
        'equity_curve': equity_curve,
        'final_equity': te.equity_total,
        'total_pnl': total_pnl,
        'win_rate': win_rate
    }

def main():
    """Main function exactly like simulate_trading_new.py"""
    parser = argparse.ArgumentParser("Exact simulation of live trading logic")
    parser.add_argument("--cfg", type=Path, default="strategyConfig_scalp_1s.json")
    parser.add_argument("--start", type=str, required=True, help="Start date (YYYY-MM-DD)")
    parser.add_argument("--end", type=str, required=True, help="End date (YYYY-MM-DD)")
    parser.add_argument("--out-trades", type=str, help="Output file for trades CSV")
    parser.add_argument("--out-equity", type=str, help="Output file for equity curve CSV")
    parser.add_argument("--log-level", default="INFO", choices=["DEBUG", "INFO", "WARNING", "ERROR"])

    args = parser.parse_args()
    log.setLevel(args.log_level.upper())

    try:
        # Load configuration
        config = load_config(args.cfg)

        # Run exact simulation
        results = run_exact_simulation(
            config=config,
            start_date=args.start,
            end_date=args.end,
            out_trades=args.out_trades,
            out_equity=args.out_equity
        )

        log.info("🎉 EXACT simulation completed successfully!")
        log.info("💡 Results should be IDENTICAL to simulate_trading_new.py")
        log.info("🚀 Live trading will now behave exactly like this simulation!")

    except Exception as e:
        log.error(f"❌ Simulation failed: {e}")
        import traceback
        log.error(traceback.format_exc())
        sys.exit(1)

if __name__ == "__main__":
    main()
